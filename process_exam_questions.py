#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考题数据处理脚本
从Excel文件中提取考题，进行数据清洗，并转换为指定的JSON格式
"""

import pandas as pd
import json
import re
import openpyxl
from openpyxl import Workbook


def clean_text(text):
    """
    数据清洗函数：去除无效字符，包括换行符、回车符、首尾空格等
    """
    if pd.isna(text) or text is None:
        return ""
    
    # 转换为字符串
    text = str(text)
    
    # 去除首尾空格
    text = text.strip()
    
    # 去除换行符和回车符
    text = re.sub(r'[\r\n]+', ' ', text)
    
    # 去除多余的空格（将多个连续空格替换为单个空格）
    text = re.sub(r'\s+', ' ', text)
    
    # 去除其他控制字符
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    # 再次去除首尾空格
    text = text.strip()
    
    return text


def format_question(row, task_id):
    """
    将题目数据格式化为指定的JSON格式
    """
    # 清洗各个字段
    question = clean_text(row['question'])
    option_a = clean_text(row['optionA'])
    option_b = clean_text(row['optionB'])
    option_c = clean_text(row['optionC'])
    option_d = clean_text(row['optionD'])
    answer = clean_text(row['answer'])
    
    # 构建完整的题目文本
    query = f"{question} A. {option_a} B. {option_b} C. {option_c} D. {option_d}"
    
    # 构建JSON格式
    data_part = {
        "data": {
            "task": str(task_id),
            "query": query
        }
    }
    
    answer_part = {
        "answer": answer
    }
    
    return data_part, answer_part


def process_exam_questions():
    """
    主处理函数
    """
    try:
        # 读取Excel文件的第二个sheet
        print("正在读取Excel文件...")
        df = pd.read_excel('题目综合.xlsx', sheet_name='精选')
        
        print(f"共找到 {len(df)} 道题目")
        
        # 处理所有题目
        processed_questions = []
        
        for i, row in df.iterrows():
            task_id = i + 1  # 题目编号从1开始
            
            try:
                data_part, answer_part = format_question(row, task_id)
                
                # 将两部分合并为一行JSON字符串
                json_line = json.dumps(data_part, ensure_ascii=False) + " " + json.dumps(answer_part, ensure_ascii=False)
                processed_questions.append(json_line)
                
                print(f"已处理第 {task_id} 题")
                
            except Exception as e:
                print(f"处理第 {task_id} 题时出错: {e}")
                continue
        
        # 创建新的Excel文件
        print("正在创建新的Excel文件...")
        wb = Workbook()
        ws = wb.active
        ws.title = "处理后的考题"
        
        # 添加表头
        ws['A1'] = "题目编号"
        ws['B1'] = "JSON格式数据"
        
        # 添加数据
        for i, json_line in enumerate(processed_questions, start=2):
            ws[f'A{i}'] = i - 1  # 题目编号
            ws[f'B{i}'] = json_line
        
        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 100
        
        # 保存文件
        output_filename = "处理后的考题.xlsx"
        wb.save(output_filename)
        
        print(f"处理完成！共处理了 {len(processed_questions)} 道题目")
        print(f"结果已保存到: {output_filename}")
        
        # 同时保存为文本文件，方便查看
        with open("处理后的考题.txt", "w", encoding="utf-8") as f:
            for json_line in processed_questions:
                f.write(json_line + "\n")
        
        print("同时保存了文本格式文件: 处理后的考题.txt")
        
        # 显示前3个示例
        print("\n前3个处理结果示例:")
        for i, json_line in enumerate(processed_questions[:3], 1):
            print(f"第{i}题: {json_line}")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return False


if __name__ == "__main__":
    print("开始处理考题数据...")
    success = process_exam_questions()
    
    if success:
        print("\n处理成功完成！")
    else:
        print("\n处理失败，请检查错误信息。")
