import json
import pandas as pd
import os
import argparse
import sys
from openpyxl import load_workbook
from openpyxl.styles import Ali<PERSON>ment, Font, PatternFill, Border, Side
from openpyxl.chart import BarChart, Reference
from openpyxl.utils import get_column_letter

def convert_json_to_excel(json_file_path, excel_file_path, add_stats=False, columns=None):
    # 检查输入文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误：输入文件 '{json_file_path}' 不存在！")
        return False
    
    # 读取JSON文件
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        print(f"成功读取JSON文件，共有{len(data)}条记录")
    except json.JSONDecodeError:
        print(f"错误：'{json_file_path}' 不是有效的JSON文件！")
        return False
    except Exception as e:
        print(f"读取JSON文件时出错: {e}")
        return False
    
    # 检查数据是否为列表
    if not isinstance(data, list):
        print(f"错误：JSON文件必须包含题目列表！")
        return False
    
    # 检查数据是否为空
    if len(data) == 0:
        print(f"警告：JSON文件中没有题目数据！")
    
    # 将JSON数据转换为DataFrame
    try:
        # 处理reference_pages字段，将列表转换为字符串
        for item in data:
            if 'reference_pages' in item and isinstance(item['reference_pages'], list):
                item['reference_pages'] = ', '.join(str(page) for page in item['reference_pages'])
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 如果指定了列，则只保留这些列
        if columns:
            # 检查指定的列是否存在
            missing_columns = [col for col in columns if col not in df.columns]
            if missing_columns:
                print(f"警告：以下列在数据中不存在: {', '.join(missing_columns)}")
            
            # 只保留存在的列
            valid_columns = [col for col in columns if col in df.columns]
            if not valid_columns:
                print("错误：没有有效的列可以导出！")
                return False
            
            df = df[valid_columns]
            print(f"已筛选指定列: {', '.join(valid_columns)}")
        
        print("成功将JSON数据转换为DataFrame")
    except Exception as e:
        print(f"转换数据时出错: {e}")
        return False

    # 将DataFrame保存为Excel文件
    try:
        # 检查输出目录是否存在，如果不存在则创建
        output_dir = os.path.dirname(excel_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"已创建输出目录: {output_dir}")
        
        df.to_excel(excel_file_path, index=False, engine='openpyxl')
        print(f"成功将数据保存到Excel文件: {os.path.abspath(excel_file_path)}")
        
        # 使用openpyxl格式化Excel文件
        wb = load_workbook(excel_file_path)
        ws = wb.active
        ws.title = "题目列表"
        
        # 定义样式
        header_font = Font(bold=True, size=12)
        header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
        thin_border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'), 
            top=Side(style='thin'), 
            bottom=Side(style='thin')
        )
        wrap_alignment = Alignment(wrap_text=True, vertical='top')
        
        # 设置列宽（根据列名动态设置）
        column_width_map = {
            'id': 8,
            'question': 60,
            'optionA': 30,
            'optionB': 30,
            'optionC': 30,
            'optionD': 30,
            'answer': 10,
            'reason': 50,
            'reference_pages': 15,
            'difficulty': 10
        }
        
        # 获取实际的列名
        headers = [cell.value for cell in ws[1]]
        
        # 设置列宽
        for i, header in enumerate(headers, start=1):
            col_letter = get_column_letter(i)
            # 如果列名在预设宽度映射中，使用预设宽度，否则使用默认宽度20
            width = column_width_map.get(header, 20) if header else 10
            ws.column_dimensions[col_letter].width = width
        
        # 应用样式到所有单元格
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.border = thin_border
                cell.alignment = wrap_alignment
                
                # 为标题行应用特殊样式
                if cell.row == 1:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 如果需要添加统计信息
        if add_stats:
            # 创建统计工作表
            stats_ws = wb.create_sheet(title="统计信息")
            
            # 统计难度分布
            if 'difficulty' in df.columns:
                difficulty_counts = df['difficulty'].value_counts().sort_index()
                
                # 写入难度分布数据
                stats_ws['A1'] = "难度级别"
                stats_ws['B1'] = "题目数量"
                stats_ws['A1'].font = header_font
                stats_ws['B1'].font = header_font
                
                row = 2
                for difficulty, count in difficulty_counts.items():
                    stats_ws[f'A{row}'] = difficulty
                    stats_ws[f'B{row}'] = count
                    row += 1
                
                # 创建难度分布图表
                chart = BarChart()
                chart.title = "题目难度分布"
                chart.x_axis.title = "难度级别"
                chart.y_axis.title = "题目数量"
                
                data = Reference(stats_ws, min_col=2, min_row=1, max_row=row-1, max_col=2)
                cats = Reference(stats_ws, min_col=1, min_row=2, max_row=row-1)
                chart.add_data(data, titles_from_data=True)
                chart.set_categories(cats)
                
                stats_ws.add_chart(chart, "D2")
            else:
                print("警告：未找到'difficulty'字段，无法生成难度分布统计")
            
            # 统计答案分布
            if 'answer' in df.columns:
                answer_counts = df['answer'].value_counts()
                
                # 写入答案分布数据
                stats_ws['A10'] = "答案选项"
                stats_ws['B10'] = "题目数量"
                stats_ws['A10'].font = header_font
                stats_ws['B10'].font = header_font
                
                row = 11
                for answer, count in answer_counts.items():
                    stats_ws[f'A{row}'] = answer
                    stats_ws[f'B{row}'] = count
                    row += 1
                
                # 创建答案分布图表
                answer_chart = BarChart()
                answer_chart.title = "答案选项分布"
                answer_chart.x_axis.title = "答案选项"
                answer_chart.y_axis.title = "题目数量"
                
                data = Reference(stats_ws, min_col=2, min_row=10, max_row=row-1, max_col=2)
                cats = Reference(stats_ws, min_col=1, min_row=11, max_row=row-1)
                answer_chart.add_data(data, titles_from_data=True)
                answer_chart.set_categories(cats)
                
                stats_ws.add_chart(answer_chart, "D15")
            else:
                print("警告：未找到'answer'字段，无法生成答案分布统计")
            
            # 设置统计工作表的列宽
            stats_ws.column_dimensions['A'].width = 15
            stats_ws.column_dimensions['B'].width = 15
            stats_ws.column_dimensions['D'].width = 15
            stats_ws.column_dimensions['E'].width = 15
            
            print("已添加统计信息工作表")
        
        # 保存格式化后的Excel文件
        wb.save(excel_file_path)
        print("Excel文件格式化完成")
        
    except PermissionError:
        print(f"错误：无法写入文件 '{excel_file_path}'，可能是文件已被打开或没有写入权限")
        return False
    except Exception as e:
        print(f"保存或格式化Excel文件时出错: {e}")
        return False
    
    return True

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='将JSON文件转换为Excel文件')
    parser.add_argument('-i', '--input', default='题目综合.json', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', default='题目综合.xlsx', help='输出Excel文件路径')
    parser.add_argument('-s', '--stats', action='store_true', help='添加统计信息工作表')
    parser.add_argument('-c', '--columns', nargs='+', help='指定要包含的列（例如：-c id question answer）')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 执行转换
    success = convert_json_to_excel(args.input, args.output, args.stats, args.columns)
    
    if success:
        print("转换完成！")
        return 0
    else:
        print("转换失败！")
        return 1

def show_available_columns(json_file_path):
    """显示JSON文件中可用的列"""
    if not os.path.exists(json_file_path):
        print(f"错误：输入文件 '{json_file_path}' 不存在！")
        return False
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        if not isinstance(data, list) or len(data) == 0:
            print("错误：JSON文件必须包含非空题目列表！")
            return False
        
        # 获取第一条记录的所有键作为列名
        columns = list(data[0].keys())
        print("\n可用的列名:")
        for col in columns:
            print(f"  - {col}")
        print("\n使用示例:")
        print(f"  python json_to_excel.py -i {json_file_path} -c {' '.join(columns[:3])}")
        
        return True
    except Exception as e:
        print(f"读取JSON文件时出错: {e}")
        return False

if __name__ == "__main__":
    # 如果没有参数，显示帮助信息
    if len(sys.argv) == 1:
        print("JSON转Excel工具 - 使用 -h 查看帮助")
        print("示例用法:")
        print("  python json_to_excel.py -i 题目综合.json -o 输出.xlsx -s")
        print("  python json_to_excel.py -i 题目综合.json -c id question answer")
        
        # 如果默认文件存在，显示可用的列
        default_file = '题目综合.json'
        if os.path.exists(default_file):
            show_available_columns(default_file)
    
    sys.exit(main())