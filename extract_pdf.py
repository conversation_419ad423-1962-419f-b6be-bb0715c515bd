import PyPDF2
import sys
import os

def extract_pdf_content(pdf_path):
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        num_pages = len(reader.pages)
        
        print(f"PDF有 {num_pages} 页")
        
        # 创建输出目录
        output_dir = "pdf_content"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 提取目录结构
        try:
            with open(os.path.join(output_dir, "toc.txt"), "w", encoding="utf-8") as toc_file:
                toc_file.write(f"PDF文件: {pdf_path}\n")
                toc_file.write(f"总页数: {num_pages}\n\n")
                toc_file.write("目录结构:\n")
                if hasattr(reader, 'outline') and reader.outline:
                    print_outline(reader.outline, 0, toc_file)
                else:
                    toc_file.write("未找到目录结构\n")
        except Exception as e:
            print(f"提取目录时出错: {e}")
        
        # 提取所有页面的内容
        print(f"\n开始提取所有 {num_pages} 页的内容...")
        
        # 每10页保存为一个文件
        batch_size = 10
        for batch_start in range(0, num_pages, batch_size):
            batch_end = min(batch_start + batch_size, num_pages)
            output_file = os.path.join(output_dir, f"content_pages_{batch_start+1}-{batch_end}.txt")
            
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(f"页码范围: {batch_start+1} - {batch_end}\n\n")
                
                for i in range(batch_start, batch_end):
                    page = reader.pages[i]
                    text = page.extract_text()
                    f.write(f"\n--- 第 {i+1} 页 ---\n\n")
                    f.write(text)
                    f.write("\n" + "-"*50 + "\n")
            
            print(f"已保存页码 {batch_start+1}-{batch_end} 到文件 {output_file}")

def print_outline(outline, level, file):
    if isinstance(outline, list):
        for item in outline:
            print_outline(item, level, file)
    elif isinstance(outline, dict):
        indent = "  " * level
        if '/Title' in outline:
            title = outline['/Title']
            file.write(f"{indent}- {title}\n")
        if '/Kids' in outline and outline['/Kids']:
            for kid in outline['/Kids']:
                print_outline(kid, level + 1, file)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        extract_pdf_content(pdf_path)
    else:
        print("请提供PDF文件路径作为参数")