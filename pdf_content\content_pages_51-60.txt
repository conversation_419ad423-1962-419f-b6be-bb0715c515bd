页码范围: 51 - 60


--- 第 51 页 ---

    0474F110 F90F131B B6F6D929 9A23A41E F1AB1666 AC4BE4EE EF2CD876
    2B633F80 DD5CF42F 147A722F DE527F39 247F3744 C23296BE FE3BE502
    EEF7D9EC BC28A576 7E
=====================================
# 将客户端上产生的 SM2公钥传送到服务器端。
[~SSH Server] sm2 peer-public-key sm2key001
Enter "SM2 public key" view, return system view with "peer-public-key end".
[*SSH Server-sm2-public-key] public-key-code begin
Enter "SM2 key code" view, return last view with "public-key-code end".
[*SSH Server-sm2-public-key-sm2-key-code] 0474F110 F90F131B B6F6D929 9A23A41E F1AB1666
[*SSH Server-sm2-public-key-sm2-key-code] AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F
[*SSH Server-sm2-public-key-sm2-key-code] DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC
[*SSH Server-sm2-public-key-sm2-key-code] BC28A576 7E
[*SSH Server-sm2-public-key-sm2-key-code] public-key-code end
[*SSH Server-sm2-public-key] peer-public-key end
[*SSH Server] commit
步骤4为SSH用户Client002 绑定SSH客户端的 SM2公钥。
[~SSH Server] ssh user client002 assign sm2-key sm2key001
[*SSH Server] commit
步骤5SSH服务器端 SFTP服务使能
# 使能SFTP服务功能
[~SSH Server] interface LoopBack 0
[~SSH Server-LoopBack0] ip address ******** ***************
[*SSH Server-LoopBack0] quit
[*SSH Server] sftp server enable
[*SSH Server] ssh server-source  -i loopback 0
[*SSH Server] commit
步骤6配置SSH用户的服务方式和授权目录
目前SSH服务器端已配置了两个 SSH用户： Client001 和Client002 ，Client001 的认证方
式是password ，Client002 的认证方式是 SM2。
[*SSH Server]  ssh user client001 service-type sftp
[*SSH Server]  ssh user client001 sftp-directory cfcard ：
[*SSH Server]  ssh user client002 service-type sftp
[*SSH Server] ssh user client002 sftp-directory cfcard ：
步骤7SFTP客户端连接 SSH服务器
# 第一次登录，需要使能 SSH客户端首次认证功能。
使能客户端 Client001 首次认证功能。
<HUAWEI> system-view
[~HUAWEI] sysname client001
[*HUAWEI] commit
[~client001] ssh client first-time  enable
[*client001] commit
使能客户端 Client002 首次认证功能
[~client002] ssh client first-time  enable
[*client002] commit
# SFTP客户端 Client001 用password 认证方式连接 SSH服务器。
[~client001] sftp ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...      
Please input the username:client001
Enter password:   HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 327
--------------------------------------------------

--- 第 52 页 ---

# SFTP客户端 Client002 用SM2认证方式连接 SSH服务器。
[~client002] sftp ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...      
The server is not authenticated. Continue to access it? [Y/N] :y 
Save the server's public key? [Y/N] :y 
The server's public key will be saved with the name ********. Please wait.
Please input the username: client002
步骤8检查配置结果
# 查看SSH状态信息。
[~SSH Server] display ssh server status
SSH Version                                : 2.0
SSH authentication timeout (Seconds)       : 60
SSH authentication retries (Times)         : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility              : Enable
SSH server keepalive                       : Disable
SFTP IPv4 server                           : Enable
SFTP IPv6 server                           : Enable
STELNET IPv4 server                        : Enable
STELNET IPv6 server                        : Enable
SNETCONF IPv4 server                       : Enable
SNETCONF IPv6 server                       : Enable
SNETCONF IPv4 server port(830)             : Disable
SNETCONF IPv6 server port(830)             : Disable
SCP IPv4 server                            : Enable
SCP IPv6 server                            : Enable
SSH port forwarding                        : Disable
SSH IPv4 server port                       : 22
SSH IPv6 server port                       : 22
ACL name                                   :
ACL number                                 :
ACL6 name                                  : 
ACL6 number                                :
SSH server ip-block                        : Enable
# 查看SSH用户信息。
[~SSH Server] display ssh user-information
----------------------------------------------------
Username                : client001
Authentication-type     : password
User-public-key-name    : 
User-public-key-type    : -
Sftp-directory          : cfcard:
Service-type            : sftp
Username                : client002
Authentication-type     : sm2
User-public-key-name    : sm2key001
User-public-key-type    : SM2
Sftp-directory          : cfcard:
Service-type            : sftp
----------------------------------------------------
Total 2, 2 printed
----结束
配置文件
● SSH 服务器上的配置文件
#
 sysname SSH ServerHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 328
--------------------------------------------------

--- 第 53 页 ---

#
interface GigabitEthernet0/0/0   
 undo shutdown 
 ip address ******** *********** 
#
sm2 peer-public-key sm2key001
public-key-code begin
    0474F110 F90F131B B6F6D929 9A23A41E F1AB1666
    AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F
    DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC
    BC28A576 7E
#
interface loopback 0
 ip address ******** ***************
sftp server enable
ssh server-source -i loopback 0
ssh user client001
ssh user client001 authentication-type password
ssh user client001 sftp-directory cfcard:
ssh user client001 service-type sftp
ssh user client002
ssh user client002 assign sm2-key sm2key001
ssh user client002 authentication-type sm2
ssh authorization-type default root
ssh user client002 sftp-directory cfcard:
ssh user client002 service-type sftp
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
ssh server hmac sha2_512 sha2_256 
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
aaa
 local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
 local-user client001 level 3
 local-user client001 service-type ssh
#
user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
#
return
● SSH 客户端 Client001 的配置文件
#
 sysname client001
#
interface GigabitEthernet0/0/0
 undo shutdown
 ip address ******** ***********
#
 ssh client first-time  enable
#
return
● SSH 客户端 Client002 的配置文件
#
 sysname client002
#
interface GigabitEthernet0/0/0
 undo shutdownHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 329
--------------------------------------------------

--- 第 54 页 ---

ip address ******** ***********
#
ssh client first-time  enable
ssh client assign sm2-host-key sm2key001
#
ssh client publickey sm2
#
return
********* 配置SSH服务器支持其他端口号访问的示例
在本示例中，通过设定 SSH服务器端的侦听端口号为其他端口号，实现只有合法的用
户才能建立 SSH连接。
组网需求
SSH协议的标准侦听端口号为 22，如果攻击者不断访问标准端口，将会使带宽和服务
器性能不断下降，从而导致其他正常用户无法访问。
设定SSH服务器端的侦听端口号为其他端口号，攻击者并不知道 SSH侦听端口号的更
改，仍然发送标准端口号 22的socket连接， SSH服务器检测发现请求连接端口号不是
侦听的端口号，就不建立 socket连接。
这样只有合法的用户采用 SSH服务器设定的非标准侦听端口才能建立 socket连接，进行
SSH协议的版本号协商、算法协商及会话密钥生成、认证、会话请求、会话阶段等过
程。
图1-76 配置SSH服务器支持其他端口号访问组网图
说明
本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。
 
配置思路
采用如下的思路配置 SSH服务器支持其他端口号访问的示例：
1.在SSH服务器上配置用户 client001 和client002 ，分别使用不同的认证方式登录
SSH服务器。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 330
--------------------------------------------------

--- 第 55 页 ---

2.分别在 STelnet客户端 Client002 和SSH服务器端生成本地密钥对，并为用户
client002 绑定SSH客户端的 RSA公钥，实现客户端登录服务器端时，对客户端进行
验证。
3. SSH 服务器端 STelnet和SFTP服务使能。
4.配置SSH用户的服务方式和授权目录。
5.配置SSH服务器侦听端口号，实现客户端以其他端口号访问服务器。
6.用户client001 和client002 分别以 STelnet和SFTP方式登录 SSH服务器。
数据准备
为完成此配置例，需准备如下的数据：
●用户client001 ，登录验证方式为 password ，以STelnet方式登录 SSH服务器。
●用户client002 ，验证方式为 RSA，并为其分配公钥 RsaKey001 ，以SFTP方式登录
SSH服务器。
● SSH 服务器的 IP地址为 ********。
● SSH 服务器端侦听端口号为 1025。
操作步骤
步骤1在服务器端生成本地密钥对
<HUAWEI> system-view
[~HUAWEI] sysname SSH Server
[*HUAWEI] commit
[~SSH Server] rsa local-key-pair create
The key name will be: CE1_Host  
The range of public key size is (2048, 3072).  
NOTE: Key pair generation will take a short while.  
Please input the modulus [default = 3072]:3072
[*SSH Server] commit
步骤2配置服务器端 RSA公钥
说明
SSH用户主要有 Password 、RSA、password-rsa 、ECC、password-ecc 、DSA、password-dsa 、
SM2、password-sm2 或all这几种认证方式：
●如果SSH用户的认证方式为 password 、password-rsa 、password-dsa 、password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。
●如果SSH用户的认证方式为 RSA、password-rsa 、DSA、password-dsa 、SM2、password-
sm2、ECC、password-ecc 和all，服务器端应保存 SSH客户端的 RSA、DSA、SM2或ECC公
钥。
# 客户端生成客户端的本地密钥对。
<HUAWEI> system-view
[~HUAWEI] sysname client002
[*HUAWEI] commit
[~client002] rsa local-key-pair create
[*client002] commit
# 查看客户端上生成 RSA公钥。
[~client002] display rsa local-key-pair public
=====================================================
Time of Key pair created: 16:38:51  2007/5/25
Key name: client002_Host
Key type: RSA encryption KeyHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 331
--------------------------------------------------

--- 第 56 页 ---

=====================================================
Key code:
3047
  0240
    BFF35E4B C61BD786 F907B5DE 7D6770C3 E5FD17AB
    203C8FCB BBC8FDF2 F7CB674E 519E8419 0F6B97A8
    EA91FC4B B9E18836 5E74BFD5 4C687767 A89C6B43
    1D7E3E1B
  0203
    010001
Host public key for PEM format code:
---- BEGIN SSH2 PUBLIC KEY ----
AAAAB3NzaC1yc2EAAAADAQABAAAAQQC/815LxhvXhvkHtd59Z3DD5f0XqyA8j8u7
yP3y98tnTlGehBkPa5eo6pH8S7nhiDZedL/VTGh3Z6ica0Mdfj4b
---- END SSH2 PUBLIC KEY ----
Public key code for pasting into OpenSSH authorized_keys file :
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAQQC/815LxhvXhvkHtd59Z3DD5f0XqyA8j8u7yP3y98tn
TlGehBkPa5eo6pH8S7nhiDZedL/VTGh3Z6ica0Mdfj4b rsa-key
=====================================================
Time of Key pair created: 16:38:51  2007/5/25
Key name: client002_Server
Key type: RSA encryption Key
=====================================================
Key code:
3067
  0260
    BCFAC085 49A2E70E 1284F901 937D7B63 D7A077AB
    ******** 4BCA86C0 4CD18B70 5DFAC9D3 9A3F3E74
    9B2AF4CB 69FA6483 E87DA590 7B47721A 16391E27
    1C76ABAB 743C568B 1B35EC7A 8572A096 BCA9DF0E
    BC89D3DB 5A83698C 9063DB39 A279DD89
  0203
    010001
# 将客户端上产生的 RSA公钥传送到服务器端。
[~SSH Server] rsa peer-public-key RsaKey001
Enter "RSA public key" view, return system view with "peer-public-key end".
[*SSH Server-rsa-public-key] public-key-code begin
Enter "RSA key code" view, return last view with "public-key-code end".
[*SSH Server-rsa-key-code] 3047
[*SSH Server-rsa-key-code] 0240
[*SSH Server-rsa-key-code] BFF35E4B C61BD786 F907B5DE 7D6770C3 E5FD17AB
[*SSH Server-rsa-key-code] 203C8FCB BBC8FDF2 F7CB674E 519E8419 0F6B97A8
[*SSH Server-rsa-key-code] EA91FC4B B9E18836 5E74BFD5 4C687767 A89C6B43
[*SSH Server-rsa-key-code] 1D7E3E1B
[*SSH Server-rsa-key-code] 0203
[*SSH Server-rsa-key-code] 010001
[*SSH Server-rsa-key-code] public-key-code end
[*SSH Server-rsa-public-key] peer-public-key end
[*SSH Server-rsa-public-key] commit
步骤3在服务器端创建 SSH用户
# 配置VTY用户界面。
[~SSH Server] user-interface vty 0 4
[~SSH Server-ui-vty0-4]  authentication-mode aaa
[*SSH Server-ui-vty0-4] protocol inbound ssh
[*SSH Server-ui-vty0-4] commit  
[~SSH Server-ui-vty0-4] quit
●创建SSH用户Client001 。
# 新建用户名为 Client001 的SSH用户，且认证方式为 password 。
[~SSH Server] ssh user client001
[*SSH Server] ssh user client001 authentication-type password
[*SSH Server] commit
# 为SSH用户Client001 配置密码。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 332
--------------------------------------------------

--- 第 57 页 ---

[~SSH Server] aaa
[*SSH Server-aaa] local-user client001 password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
说明
设置的密码必须满足以下要求：
–密码采取交互式输入，系统不回显输入的密码。
–输入的密码为字符串形式，区分大小写，长度范围是 8～16。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。
–特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。
▪如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
▪如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。
配置文件中将以密文形式体现设置的密码。
[*SSH Server-aaa] local-user client001 service-type ssh
[*SSH Server-aaa] commit
[~SSH Server-aaa] quit
# 配置Client001 的服务方式为 STelnet。
[*SSH Server] ssh user client001 service-type stelnet
●创建SSH用户Client002 。
# 新建用户名为 Client002 的SSH用户，且认证方式为 RSA，并绑定 SSH客户端 RSA
公钥。
[~SSH Server] ssh user client002
[*SSH Server] ssh user client002 authentication-type rsa
[*SSH Server] ssh user client002 assign rsa-key RsaKey001
[*SSH Server] commit
# 配置Client002 的服务方式为 SFTP，并为其配置授权目录。
[~SSH Server]  ssh user client002 service-type sftp
[*SSH Server] ssh user client002 sftp-directory cfcard:
[*SSH Server] commit
步骤4SSH服务器端 STelnet和SFTP服务使能
[~SSH Server] interface LoopBack 0
[~SSH Server-LoopBack0] ip address ******** ***************
[*SSH Server-LoopBack0] quit
[*SSH Server] stelnet server enable
[*SSH Server] sftp server enable
[*SSH Server] ssh server-source  -i loopback 0
[*SSH Server] commit
步骤5配置SSH服务端新的侦听端口号
[*SSH Server] ssh server port 1025
步骤6SSH客户端连接 SSH服务器
# 第一次登录，则需要使能 SSH客户端首次认证功能。
使能客户端 Client001 首次认证功能。
<HUAWEI> system-view
[~HUAWEI] sysname client001
[*HUAWEI] commit
[~client001] ssh client first-time  enableHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 333
--------------------------------------------------

--- 第 58 页 ---

[*client001] commit
使能客户端 Client002 首次认证功能
[*client002] ssh client first-time  enable
[*client002] commit
# STelnet 客户端用新端口号连接 SSH服务器。
[~client001] stelnet ******** 1025
Please input the username:client001
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait...
Enter password:   
显示登录成功信息如下：
Info: The max number of VTY users is 10, and the number
      of current VTY users on line is 1.       
<SSH Server>
# SFTP客户端用新端口号连接 SSH服务器。
[~client002] sftp ******** 1025
Please input the username:client002
Trying ******** ...
Press CTRL+K to abort
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait.
..
sftp-client>
步骤7检查配置结果
# 攻击者使用原端口号 22访问SSH服务器，不能成功。
[~client002] sftp ********
Please input the username:client002
Trying ******** ...
Press CTRL+K to abort
Error: Failed to connect to the server.      
# 查看SSH状态信息。
[~SSH Server] display ssh server status
SSH Version                                : 2.0
SSH authentication timeout (Seconds)       : 60
SSH authentication retries (Times)         : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility              : Enable
SSH server keepalive                       : Disable
SFTP IPv4 server                           : Enable
SFTP IPv6 server                           : Enable
STELNET IPv4 server                        : Enable
STELNET IPv6 server                        : Enable
SNETCONF IPv4 server                       : Enable
SNETCONF IPv6 server                       : Enable
SNETCONF IPv4 server port(830)             : Disable
SNETCONF IPv6 server port(830)             : Disable
SCP IPv4 server                            : Enable
SCP IPv6 server                            : Enable
SSH port forwarding                        : Disable
SSH IPv4 server port                       : 22
SSH IPv6 server port                       : 22
ACL name                                   :HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 334
--------------------------------------------------

--- 第 59 页 ---

ACL number                                 :
ACL6 name                                  : 
ACL6 number                                :
SSH server ip-block                        : Enable
----结束
配置文件
● SSH 服务器上的配置文件
#
 sysname SSH Server
#
rsa peer-public-key rsakey001
public-key-code begin
308188
  028180
    B21315DD 859AD7E4 A6D0D9B8 121F23F0 006BB1BB A443130F 7CDB95D8 4A4AE2F3
    D94A73D7 36FDFD5F 411B8B73 3CDD494A 236F35AB 9BBFE19A 7336150B 40A35DE6
    2C6A82D7 5C5F2C36 67FBC275 2DF7E4C5 1987178B 8C364D57 DD0AA24A A0C2F87F
    474C7931 A9F7E8FE E0D5A1B5 092F7112 660BD153 7FB7D5B2 171896FB 1FFC38CD
  0203
    010001
public-key-code end
peer-public-key end
#
interface loopback 0
 ip address ******** ***************
stelnet server enable
sftp server enable
ssh server-source -i loopback 0
ssh server port 1025
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 authentication-type rsa
ssh user client002 assign rsa-key rsakey001
ssh user client002 sftp-directory cfcard:
ssh user client002 service-type sftp
#
aaa
 local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
 local-user client001 service-type ssh
 #
interface GigabitEthernet0/0/0
 undo shutdown
 ip address ******** ***********
#
user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
#
return
● SSH 客户端 Client001 的配置文件
#
 sysname client001
#
interface GigabitEthernet0/0/0
 undo shutdown
ip address ******** ***********
#
ssh client first-time  enable
#
return
● SSH 客户端 Client002 的配置文件HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 335
--------------------------------------------------

--- 第 60 页 ---

#
 sysname client002
#
interface GigabitEthernet0/0/0
 undo shutdown
 ip address ******** ***********
#
ssh client first-time  enable
#
return
********* 配置公网 SSH客户端访问私网 SSH服务器的示例
配置公网 SSH客户端访问私网 SSH服务器的示例。在本示例中，通过配置公网用户的
SSH相关属性信息，实现公网用户分别以 STelnet方式和 SFTP方式访问私网设备。
组网需求
如图1-77所示，作为 SSH客户端的设备 PE1位于MPLS骨干网中，作为 SSH服务器的 CE1
位于AS号为65410的私网中。公网用户可以通过 PE1安全地访问和管理私网设备 CE1。
图1-77 配置公网 SSH客户端访问私网 SSH服务器组网图
说明
本例中的 Interface1 ，Interface2 ，Interface3 分别代表接口 GE1/0/1、GE2/0/1和GE1/0/2。
配置思路
采用如下的思路配置 SSH支持私网访问：
1.在作为 SSH客户端的 PE设备上配置 VPN实例，实现将 CE接入PE。
2.在PE与CE之间建立 EBGP对等体关系，引入 VPN路由。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 336
--------------------------------------------------
