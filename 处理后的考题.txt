{"data": {"task": "1", "query": "ZTP自动部署时，若设备下次启动配置文件不为NULL或vrpcfg.zip，会导致什么结果？ A. ZTP会覆盖配置文件 B. ZTP不启动 C. 设备进入恢复模式 D. 自动生成新配置"}} {"answer": "B"}
{"data": {"task": "2", "query": "通过TFTP下载文件时，若目标文件已存在，系统默认的交互提示是什么？ A. 直接覆盖 B. 询问是否覆盖 C. 跳过下载 D. 报错退出"}} {"answer": "B"}
{"data": {"task": "3", "query": "使用SCP下载文件时，客户端必须使能哪个功能才能首次连接服务器？ A. ssh client first-time enable B. scp server enable C. ssh server-source D. aaa authentication-mode"}} {"answer": "A"}
{"data": {"task": "4", "query": "在VPN场景下，PE设备通过STelnet访问CE时，必须附加哪个参数指定VPN实例？ A. -vpn-instance B. -vrf C. -instance D. -vpn"}} {"answer": "A"}
{"data": {"task": "5", "query": "在STelnet RSA认证配置中，客户端公钥上传至服务器后，需要执行哪个命令绑定到用户？ A. ssh user assign rsa-key B. ssh user bind-key C. ssh user client002 assign rsa-key rsakey001 D. rsa-key bind user"}} {"answer": "C"}
{"data": {"task": "6", "query": "配置SSH服务器时，hmac算法被强制要求包含哪两种？ A. md5和sha1 B. sha2_512和sha2_256 C. sha1和sha256 D. md5和sha512"}} {"answer": "B"}
{"data": {"task": "7", "query": "ZTP过程中，若DHCP服务器未设置Option 67会导致什么结果？ A. 使用默认文件名 B. ZTP退出 C. 自动搜索文件 D. 无限等待"}} {"answer": "B"}
{"data": {"task": "8", "query": "在STelnet ECC认证中，客户端生成的ECC密钥模数可选值不包括哪个？ A. 256 B. 384 C. 512 D. 521"}} {"answer": "C"}
{"data": {"task": "9", "query": "ZTP自动部署时，DHCP服务器地址租期不支持什么操作？ A. 续租 B. 释放 C. 重新申请 D. 老化"}} {"answer": "A"}
{"data": {"task": "10", "query": "ZTP过程中，若设备启动配置文件名为test.cfg而非vrpcfg.zip，会导致什么？ A. ZTP正常启动 B. ZTP不启动 C. 自动重命名文件 D. 配置文件被覆盖"}} {"answer": "B"}
{"data": {"task": "11", "query": "在VPN场景下，PE设备ping CE时必须指定的参数是什么？ A. -vpn-instance B. -a source-ip C. -c count D. -s size"}} {"answer": "B"}
{"data": {"task": "12", "query": "通过SCP下载文件时，客户端源地址设置的命令是什么？ A. scp client-source -a B. ssh client-source C. ip scp source D. set scp source"}} {"answer": "A"}
{"data": {"task": "13", "query": "ZTP自动部署时，中间文件服务器可以是以下哪种类型？ A. 仅TFTP B. 仅FTP C. TFTP/FTP/SFTP D. HTTP/HTTPS"}} {"answer": "C"}
{"data": {"task": "14", "query": "配置SSH服务器时，cipher算法强制要求包含哪些？ A. aes128_cbc B. aes256_gcm aes128_gcm C. 3des_cbc D. blowfish_cbc"}} {"answer": "B"}
{"data": {"task": "15", "query": "ZTP过程中，修复补丁信息应配置在哪个文件中？ A. 启动配置文件 B. 中间文件 C. 系统软件包 D. 补丁文件本身"}} {"answer": "B"}
{"data": {"task": "16", "query": "在VPN场景下，PE设备BGP配置的VPN-target值是多少？ A. 100:1 B. 111:1 C. 200:1 D. 65410:1"}} {"answer": "B"}
{"data": {"task": "17", "query": "通过TFTP下载文件时，默认超时时间是多少秒？ A. 3秒 B. 5秒 C. 10秒 D. 30秒"}} {"answer": "A"}
{"data": {"task": "18", "query": "ZTP自动部署完成后，如何验证设备是否通过ZTP部署？ A. display startup B. display ztp status C. display current-configuration D. display version"}} {"answer": "B"}
{"data": {"task": "19", "query": "管理员在“公网SSH客户端访问私网SSH服务器”场景中，发现PE1无法学习到CE1的私网路由。检查配置后，PE1上的BGP配置正确。根据示例，CE1上可能遗漏了哪项关键的BGP配置？ A. 未在BGP进程下宣告直连路由 B. 未配置正确的`peer as-number` C. 未在`ipv4-family unicast`视图下使能对等体 D. 未配置`route-distinguisher`"}} {"answer": "A"}
{"data": {"task": "20", "query": "管理员在客户端使用`scp client-source -a *******`命令的目的是什么？ A. 指定SCP服务器的IP地址为******* B. 将文件上传到服务器的*******目录下 C. 设置本次SCP传输使用的源IP地址为******* D. 限制只有IP地址为*******的服务器才能连接"}} {"answer": "C"}
{"data": {"task": "21", "query": "为什么在进行ZTP部署时，手册建议为文件服务器的用户设置只读权限？ A. 为了防止设备下载文件时发生写冲突 B. 为了防止设备将本地日志上传到服务器，占用空间 C. 为了充分保证文件服务器的安全，防止版本文件被非法修改 D. 为了兼容仅支持只读操作的TFTP协议"}} {"answer": "C"}
{"data": {"task": "22", "query": "如果一台设备在ZTP过程中需要下载风河补丁（VxWorks Patch），根据手册的特性限制，ZTP流程会如何处理？ A. 正常下载并加载补丁 B. 跳过该补丁，继续其他流程 C. ZTP不支持风河补丁，可能导致流程异常或失败 D. 提示用户手动确认安装"}} {"answer": "C"}
{"data": {"task": "23", "query": "在ZTP配置中，如果需要加载一个预配置脚本，该脚本文件应该被上传到待配置设备的哪个位置？ A. DHCP服务器上 B. 文件服务器的根目录 C. 待配置设备主控板的存储介质中 D. 任意与设备相连的U盘中"}} {"answer": "C"}
{"data": {"task": "24", "query": "在SFTP ECC认证示例中，当客户端`client002`执行`display ecc local-key-pair public`时，输出的密钥类型（Key Type）是什么？ A. ECC Encryption Key B. ECDSA Key C. ECC Public Key D. NISTP521 Key"}} {"answer": "A"}
{"data": {"task": "25", "query": "在ZTP配置举例的`ztp_script.cfg`文件中，`sha256sum`字段的作用是什么？ A. 作为该中间文件本身的校验码 B. 作为即将下载的系统软件的校验码 C. 作为设备ESN的哈希值 D. 作为连接文件服务器的密码"}} {"answer": "A"}
{"data": {"task": "26", "query": "在ZTP特性限制中，提到“同一台设备不可同时和两台及以上DHCP服务器在一个广播域中”。这主要是为了避免什么问题？ A. 设备收到多个冲突的IP地址 B. 设备收到多个不同的中间文件路径，导致ZTP功能失效 C. 网络中产生广播风暴 D. DHCP服务器的CPU负载过高"}} {"answer": "B"}
{"data": {"task": "27", "query": "在`display ssh server status`的输出中，`SSH version 1.x compatibility`默认是启用还是禁用？ A. Enable B. Disable C. Auto D. 未显示该项"}} {"answer": "A"}
{"data": {"task": "28", "query": "在ZTP配置中，如果中间文件和版本文件重名，例如设备下次启动文件已是`V800R023C10SPC500.cc`，而中间文件中指定的`system_software`也是`V800R023C10SPC500.cc`，将会发生什么？ A. 设备会重新下载并覆盖现有文件 B. ZTP不会下载重名的文件 C. ZTP流程报错并退出 D. 设备会提示用户确认是否覆盖"}} {"answer": "B"}
{"data": {"task": "29", "query": "在STelnet ECC认证示例中，当服务器端创建`ecc local-key-pair`时，系统提示了三种可选的模长，但示例中通过交互输入最终选择了哪一个？ A. 256 B. 384 C. 521 D. 2048"}} {"answer": "C"}
{"data": {"task": "30", "query": "在所有STelnet示例的最终服务器配置文件中，`ssh server hmac`命令配置了哪两种HMAC算法？ A. `sha1`和`sha2_256` B. `sha2_512`和`sha2_256` C. `md5`和`sha1` D. `sha2_512`和`sha1`"}} {"answer": "B"}
{"data": {"task": "31", "query": "在ZTP配置中，关于DHCPv6地址租期的说法，以下哪项是正确的？ A. ZTP通过DHCPv6申请的IPv6地址租期至少为30分钟且支持续租 B. ZTP通过DHCPv6申请的IPv6地址租期至少为1小时且不支持续租 C. ZTP通过DHCPv6申请的IPv6地址租期至少为2小时且支持续租 D. ZTP通过DHCPv6申请的IPv6地址租期至少为24小时且不支持续租"}} {"answer": "B"}
{"data": {"task": "32", "query": "在ZTP配置中，关于文件服务器的安全建议，以下哪项是不正确的？ A. 配置的文件服务器用户名应唯一 B. 文件服务器用户权限应设置为只读 C. ZTP过程结束后应保持文件服务器功能开启以便后续维护 D. 文件服务器与空配置设备的缺省网关之间必须路由可达"}} {"answer": "C"}
{"data": {"task": "33", "query": "在ZTP配置中，如果需要配置DHCPv4 Server的Option 67来指定文件服务器地址和中间文件名，以下哪种格式是正确的？ A. sftp://user:password@***********/test.ini B. sftp://***********/user/password/test.ini C. sftp://***********:user:password/test.ini D. sftp://***********/test.ini:user:password"}} {"answer": "A"}
{"data": {"task": "34", "query": "在ZTP配置中，中间文件支持哪些扩展名？ A. .txt, .cfg, .ini B. .ini, .py, .cfg C. .py, .sh, .bat D. .cfg, .xml, .json"}} {"answer": "B"}
{"data": {"task": "35", "query": "在ZTP配置中，如果待配置设备与DHCPv6服务器不在同一网段，需要采取什么措施？ A. 配置静态路由 B. 配置DHCPv6中继 C. 使用DHCPv4代替DHCPv6 D. 配置IPv6隧道"}} {"answer": "B"}
{"data": {"task": "36", "query": "在ZTP配置中，关于中间文件的说法，以下哪项是不正确的？ A. 中间文件可以存放在文件服务器上 B. 中间文件和版本文件可以部署在同一个文件服务器上 C. 中间文件必须使用.ini扩展名 D. 中间文件可以通过DHCPv4/DHCPv6服务器的Options字段指定"}} {"answer": "C"}
{"data": {"task": "37", "query": "在ZTP配置中，如果需要开启ZTP功能，以下哪个命令是正确的？ A. ztp enable B. ztp start C. ztp function enable D. ztp service start"}} {"answer": "A"}
{"data": {"task": "38", "query": "在ZTP配置中，关于文件服务器与空配置设备的连接要求，以下哪项是正确的？ A. 文件服务器与空配置设备必须在同一网段 B. 文件服务器与空配置设备的缺省网关之间必须路由可达 C. 文件服务器必须与DHCP服务器部署在同一设备上 D. 文件服务器必须支持HTTPS协议"}} {"answer": "B"}
{"data": {"task": "39", "query": "在ZTP配置中，如果需要加载预配置脚本，这个步骤是必选的还是可选的？ A. 必选步骤 B. 可选步骤 C. 仅在使用DHCPv6时必选 D. 仅在使用DHCPv4时必选"}} {"answer": "B"}
{"data": {"task": "40", "query": "在ZTP配置中，DHCPv6 Server需要配置哪些必选的Options字段？ A. Option 5 B. Option 59 C. Option 5, Option 59 D. Option 5, Option 23"}} {"answer": "C"}
{"data": {"task": "41", "query": "在配置SSH客户端支持的公钥算法时，以下哪个命令可以同时启用DSA、ECC和RSA算法？ A. ssh client publickey dsa ecc rsa B. ssh client publickey dsa,ecc,rsa C. ssh client publickey-algorithm dsa ecc rsa D. ssh client public-key dsa ecc rsa"}} {"answer": "A"}
{"data": {"task": "42", "query": "哪些产品型号的ZTPv4不支持QinQ接口VLAN学习 A. NE40E-X16C/ NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40EX8AK B. NE40E-X16C/ NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40EX8AK C. NE40E-X16C/ NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40EX8AK D. NE40E-X16C/ NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40EX8AK"}} {"answer": "A"}
{"data": {"task": "43", "query": "以下型号中，哪个不是NE40E系列设备：NE40E-X16C/ NE40E-X16A/ NetEngine 40EX8AK/ A. NE40E-X16C/ B. NE40E-X16A/ C. NetEngine 40EX8AK/ D. 以上都是"}} {"answer": "D"}
{"data": {"task": "44", "query": "配置DHCPv4 Server时，option60包含哪些信息？ A. 设备序列号 B. 设备厂商及型号 C. 中间文件名称 D. DHCP客户端的出口网关"}} {"answer": "B"}
{"data": {"task": "45", "query": "使用DSA认证方式，配置SFTP访问文件配置，以下步骤应该如何排序？ 1. SSH服务器端SFTP服务使能。 2. 分别在SFTP客户端Client002和SSH服务器端生成本地密钥对，并为用户client002 绑定SSH客户端的DSA公钥，实现客户端登录服务器端时，对客户端进行验证。 3. 在SSH服务器上配置用户client001和client002，分别使用不同的认证方式登录 SSH服务器。 4. 用户client001和client002分别以SFTP方式登录SSH服务器，实现访问服务器上的文件。 5. 配置SSH用户的服务方式和授权目录。 A. 12345 B. 32145 C. 32154 D. 31254"}} {"answer": "C"}
{"data": {"task": "46", "query": "以下哪一项不是通过DHCP配置ZTP实现自动部署的正确思路？ A. 配置文件服务器，将文件服务器作为SFTP Server，存放中间文件及系统软件、配 置文件。 B. 编辑Python、ini或者cfg格式的中间文件，使不同的设备能够通过中间文件获取相 应的系统软件和配置文件。 C. 配置DHCP服务器和中继，使空配置设备可以获得DHCP服务器发送的DHCP信 息。 D. 使用第三方服务器作为文件服务器，配置的具体方法请参见第三方服务器的操作 指导，设置PC上SFTP的工作目录为D:\\ztp。文件服务器配置完成后，将设备需要 加载的系统软件和配置文件放在D:\\ztp目录下。"}} {"answer": "D"}
{"data": {"task": "47", "query": "以下哪项不是公网客户端访问企业内部的SSH服务器必要的数据？ A. vpn配置 B. ip地址 C. 登录方式配置 D. ssh安全配置"}} {"answer": "D"}
{"data": {"task": "48", "query": "使用sm2认证使用stelnet登录的思路通常不包含下面哪项？ A. 配置用户 B. 配置vpn C. 配置服务方式 D. 配置密钥"}} {"answer": "B"}
{"data": {"task": "49", "query": "使用sm2认证使用stelnet登录设置的密码规则不包含以下哪一项？ A. 长度范围8-12 B. 至少包含两种类型字符 C. 字母 D. 数字"}} {"answer": "A"}
{"data": {"task": "50", "query": "以下哪个命令用来验证SSH的配置状态信息 A. display ssh server status B. show ssh server status C. display ssh server state D. show ssh server state"}} {"answer": "A"}
