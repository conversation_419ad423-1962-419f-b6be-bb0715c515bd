#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的JSON格式是否正确
"""

import json

def validate_json_format():
    """
    验证生成的JSON格式
    """
    try:
        with open("处理后的考题.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        print(f"共有 {len(lines)} 行数据")
        
        valid_count = 0
        invalid_count = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                # 尝试分割JSON字符串
                parts = line.split('} {')
                if len(parts) != 2:
                    print(f"第 {i} 行格式错误：无法正确分割")
                    invalid_count += 1
                    continue
                
                # 重新组装JSON字符串
                data_json = parts[0] + '}'
                answer_json = '{' + parts[1]
                
                # 验证JSON格式
                data_obj = json.loads(data_json)
                answer_obj = json.loads(answer_json)
                
                # 验证数据结构
                if "data" not in data_obj:
                    print(f"第 {i} 行错误：缺少 'data' 字段")
                    invalid_count += 1
                    continue
                
                if "task" not in data_obj["data"] or "query" not in data_obj["data"]:
                    print(f"第 {i} 行错误：data 字段结构不正确")
                    invalid_count += 1
                    continue
                
                if "answer" not in answer_obj:
                    print(f"第 {i} 行错误：缺少 'answer' 字段")
                    invalid_count += 1
                    continue
                
                valid_count += 1
                
                # 显示前3个有效的示例
                if valid_count <= 3:
                    print(f"\n第 {i} 行验证通过:")
                    print(f"  任务ID: {data_obj['data']['task']}")
                    print(f"  题目: {data_obj['data']['query'][:50]}...")
                    print(f"  答案: {answer_obj['answer']}")
                
            except json.JSONDecodeError as e:
                print(f"第 {i} 行JSON格式错误: {e}")
                invalid_count += 1
            except Exception as e:
                print(f"第 {i} 行处理错误: {e}")
                invalid_count += 1
        
        print(f"\n验证结果:")
        print(f"  有效行数: {valid_count}")
        print(f"  无效行数: {invalid_count}")
        print(f"  总行数: {valid_count + invalid_count}")
        
        if invalid_count == 0:
            print("\n✅ 所有数据格式都正确！")
        else:
            print(f"\n❌ 发现 {invalid_count} 行格式错误")
        
        return invalid_count == 0
        
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("开始验证JSON格式...")
    success = validate_json_format()
    
    if success:
        print("\n验证成功！所有数据格式都符合要求。")
    else:
        print("\n验证失败，请检查错误信息。")
