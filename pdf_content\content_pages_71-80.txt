页码范围: 71 - 80


--- 第 71 页 ---

步骤2在SSH服务器端创建 SSH用户
# 配置VTY用户界面。
[*SSH Server]  user-interface vty 0 4
[*SSH Server-ui-vty0-4]  authentication-mode aaa
[*SSH Server-ui-vty0-4]  protocol inbound ssh
[*SSH Server-ui-vty0-4]  quit
# 新建用户名为 client001 的SSH用户，且认证方式为 password 。
[*SSH Server]  ssh user client001
Info: Succeeded in adding a new SSH user.
[*SSH Server] ssh user client001 authentication-type password
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
# 为SSH用户client001 配置密码为 %TGB6yhn7ujm 。
[*SSH Server]  aaa
[*SSH Server-aaa] local-user client001 password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
Info: A new user is added.
[*SSH Server-aaa] local-user client001 service-type ssh
[*SSH Server-aaa] local-user client001 level 3
[*SSH Server-aaa] quit
# 配置SSH用户client001 的服务方式为 all。
[*SSH Server]  ssh user client001 service-type all
步骤3在服务器端使能 SCP服务
[*SSH Server]  scp server enable
[*SSH Server]  commit
步骤4从SCP客户端下载服务器上的文件
# 第一次登录，需要使能 SSH客户端首次认证功能。
<HUAWEI>  system-view
[~HUAWEI]  sysname SCP Client
[*SCP Client] ssh client first-time  enable
# 设置SCP客户端的源地址为 LoopBack 接口IP地址*******。
[*SCP Client]  scp client-source -a *******
Info: Succeeded in setting the source address of the SCP client to *******.
# 使用aes128加密算法将文件 license.txt 从IP地址为 ************** 的远端 SCP服务器
下载至本地用户目录下。
[*SCP Client]  scp -a ******* -cipher aes128 client001@**************:license.txt license.txt
[*SCP Client]  commit
步骤5验证配置结果
在SCP客户端执行 display scp-client 命令，查看结果如下：
<HUAWEI> display scp-clientHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 347
--------------------------------------------------

--- 第 72 页 ---

 The source address of SCP client is *******.
----结束
配置文件
● SCP服务器上的配置文件
#
 sysname SSH Server
#
aaa
 local-user client001 password irreversible-cipher @%@%1-w$!gvBa#6W,ZUm2EN*BYqNWwI3BV\uV`
%_oauS;RQB%>> ~GV#QzO ~k/8;U6;@%@%
 local-user client001 service-type ssh
 local-user client001 level 3
 # 
 scp server enable 
 ssh user client001
 ssh user client001 authentication-type password
 ssh user client001 service-type all
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
ssh server hmac sha2_512 sha2_256 
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
#
return
● SCP客户端的配置文件
#
 sysname SCP Client
#
 ssh client first-time  enable
 scp client-source *******
#
return
********* 通过HTTP访问其他设备配置示例
在本示例中，通过从 HTTP客户端登录 HTTP服务器，实现从 HTTP服务器中下载证书。
组网需求
当HTTP客户端需要进行从 HTTP服务器端下载证书，可以使用 HTTP协议。如 图1-79所
示，HTTP客户端的设备和 HTTP服务器之间路由可达，用户可通过从 HTTP客户端登录
HTTP服务器，实现从 HTTP服务器中下载证书到客户端。
HTTP服务器支持 SSL策略，为了提高数据传输的安全性，建议 HTTP客户端配置 SSL策
略。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 348
--------------------------------------------------

--- 第 73 页 ---

图1-79 配置通过 HTTP访问其他设备文件组网图
 
配置思路
采用如下的思路配置通过 HTTP访问其他设备文件：
1.配置HTTP客户端的 SSL策略。
2.配置HTTP客户端。
数据准备
为完成此配置示例，需准备如下的数据：
● HTTP 客户端的 SSL策略名称 policy1。
● domain 域名domain1
操作步骤
步骤1配置HTTP客户端的 SSL策略。
# 配置PKI域
<HUAWEI> system-view
[~HUAWEI] pki domain domain1
[*HUAWEI-pki-domain-domain1] commit
[~HUAWEI-pki-domain-domain1] quit
[~HUAWEI] pki import-certificate  ca domain domain1 filename  test.crt
说明
这里以 CA证书为例，用户在实际配置过程中需要将 ca和test.crt替换为设备上已有的证书类型和
名称。用户可以自行将证书上传至设备中进行安装，也可以通过申请和下载后进行安装，详细过
程请参考《 PKI配置》中的“获取证书”章节。
# 配置SSL绑定PKI域
[~HUAWEI] ssl policy policy1
[*HUAWEI-ssl-policy-policy1] pki-domain domain1
[*HUAWEI-ssl-policy-policy1] commit
[~HUAWEI-ssl-policy-policy1] quit
步骤2配置HTTP客户端。
[~HUAWEI] http
[*HUAWEI-http] client ssl-policy policy1
[*HUAWEI-http] client ssl-verify peer 
[*HUAWEI-http] commit
[~HUAWEI-http] quit
步骤3查看HTTP客户端是否配置成功。
[~HUAWEI] display ssl policyHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 349
--------------------------------------------------

--- 第 74 页 ---

       SSL Policy Name: policy1
            PKI domain: domain1
     Policy Applicants: HTTP-CLIENT
         Key-pair Type:
 Certificate  File Type:
      Certificate  Type:
  Certificate  Filename:
     Key-file  Filename:
              CRL File:
       Trusted-CA File:
----结束
配置文件
● HTTP 客户端的配置文件
#
ssl policy policy1
 pki-domain domain1
#
http
 client ssl-policy policy1
 client ssl-verify peer
#
return
1.9 ZTP 配置
设备可以通过零配置自动部署 ZTP（Zero Touch Provisioning ）实现空配置下的上电自
动部署。
背景信息
通过配置 ZTP，可以实现空配置设备上电自动部署，在大规模部署网络设备时可通过
ZTP提高部署效率。
VS模式下，该特性仅在 Admin-VS 支持。
1.9.1 ZTP 概述
定义
ZTP是指空配置设备上电启动时采用的一种自动加载版本文件（包括系统软件、配置文
件、补丁文件）的功能。
目的
在部署网络设备时，设备硬件安装完成后，需要管理员到安装现场对设备进行软件调
试。当设备数量较多、分布较广时，管理员需要在每一台设备上进行手工配置，既影
响了部署的效率，又需要较高的人力成本。
设备运行 ZTP功能，可以从文件服务器获取版本文件并自动加载，实现设备的免现场配
置、部署，从而降低人力成本，提升部署效率。
受益
实现设备的免现场配置、部署，降低人力成本，提升部署效率。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 350
--------------------------------------------------

--- 第 75 页 ---

1.9.2 ZTP 配置注意事项
特性限制
表1-42 本特性的使用限制
特性限制 系列 涉及产品
ZTPv4不支持 QinQ接口VLAN学习，请合理规
划。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
ZTP运行过程中，禁止配置命令 set save-
configuration 、undo set save-configuration 。
否则： 1、会影响 ZTP正常运行  2、用户配置可能
失效。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
评估开局阶段周期，参照周期来设置 DHCP服务
器的地址租期，租期过短会导致上线失败。建议
为DHCP服务器地址设置长周期。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
ZTP运行过程中：
1、禁止配置 mtp assistant disable 和assistant
scheduler suspend 命令关闭 OPS内置脚本维护助
手。
2、禁止配置 undo enable(OPS 视图)关闭OPS功
能。
否则影响 ZTP功能。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
同一台设备不可同时和两台及以上 DHCP服务器
在一个广播域中，否则 ZTP功能失效。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AKHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 351
--------------------------------------------------

--- 第 76 页 ---

特性限制 系列 涉及产品
中间件中设置的待下载的 CC包、配置文件、补丁
文件等不能与设备上运行或下次启动设置的 CC
包、配置文件、补丁文件重名。 ZTP不会下载与
设备上运行的或下次启动设置的文件重名的文
件。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
下次启动使用 ZTP时，需要确认 ZTP使能状态，如
果状态是 disable，需要配置使能，否则下次无法
进入ZTP流程。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
使用ZTP时，需要 DHCP服务器指定对应的
Option，如66、67或59。如果不设置，会导致
ZTP退出。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
在使用 ZTP的场景，用户手动设置的设备启动项
（CC软件包 /配置文件 /补丁文件）可能会被 ZTP
覆盖，需要先执行 set ztp disable 命令终止 ZTP运
行。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
设备需要从 ZTP启动时，启动项中的配置文件需
要为NULL或者vrpcfg.zip ，否则 ZTP不启动。NE40E NetEngine 40E-
X8AK
设备启动时，预配置脚本生成的带 VPN的子接口
不支持 ZTP。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
ZTP(SZTP) 不支持风河补丁 ,使用ZTP(SZTP) 开局
升级不要使用风河补丁。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AKHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 352
--------------------------------------------------

--- 第 77 页 ---

特性限制 系列 涉及产品
ZTPv6限制约束如下：
1、不能与上游 Eth-trunk 接口对接；
2、QinQ接口不能 VLAN学习；
3、地址不会冲突探测，可能会导致申请到的 IPv6
地址冲突；
4、下线报文不会发送 Release报文， Server收不
到地址释放消息，只能依赖地址老化机制。NE40E NE40E-X16C/
NE40E-X8C/
NE40E-X8A/
NE40E-X3A/
NE40E-X16A/
NetEngine 40E-
X8AK
 
1.9.3 配置通过 DHCP实现ZTP自动部署
通过DHCP完成ZTP实现自动部署以降低人力成本，提升部署效率。
应用环境
在部署网络设备时，设备硬件安装完成后，需要管理员到安装现场对设备进行软件调
试。当设备数量较多、分布较广时，管理员需要在每一台设备上进行手工配置，既影
响了部署的效率，又需要较高的人力成本。
设备运行 ZTP功能，通过 DHCP实现自动部署，实现设备的免现场配置、部署，从而降
低人力成本，提升部署效率。
前置任务
在配置 ZTP之前，需要完成以下任务：
● DHCP 服务器、文件服务器到待配置设备的网关间路由可达。
●确保待配置设备中没有启动配置文件。
1.9.3.1 编辑中间文件
操作步骤
步骤1中间文件可以是 ini文件、 cfg文件或 Python脚本文件。其中， ini文件和 cfg文件的使用
要求低且配置简单， Python脚本文件对用户要求高，所以推荐首次使用 ZTP的用户选
择ini文件或者 cfg文件作为中间文件。文件格式请参见 ini格式的中间文件、 cfg格式的
中间文件或 Python格式的中间文件。
----结束
******* 配置DHCPv4 Server 和DHCPv4 Relay
背景信息
需要运行 ZTP的设备在上电之前，须先部署 DHCPv4服务器，以确保作为 DHCP客户端
的空配置设备能正常获取到 IP地址、网关及中间文件服务器地址、中间文件名称等信
息。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 353
--------------------------------------------------

--- 第 78 页 ---

运行ZTP的设备进入 DHCP discover 阶段后，在发送 DHCP discover 消息时会携带 DHCP
option 60 和61。DHCP option 60 (Vendor class identifier) 用来携带设备厂商及型号
信息； DHCP option 61 (Client-identifier) 用来携带设备序列号。
DHCPv4服务器上需配置的 Options字段见表1-43。
表1-43 Options 字段说明
Option编号 是否可选 Option作用
1 必选 设置IP地址的子网掩码。
3 必选 设置DHCP客户端的出口网关。
6 可选 设置DNS服务器的 IP地址。当用户设置中间文件服
务器的主机名为域名类型时（如
“www.ztp.com ”），需要部署 DNS服务器来将域
名转换为相应的 IP地址。如果设置的主机名为 IP地
址，则不需要再部署 DNS服务器。
66 可选 设置中间文件服务器的主机名。文件服务器可以是
TFTP/FTP/SFTP 服务器，格式如下：
● tftp:// hostname
● ftp://[ username [:password ]@]hostname
● sftp://
[username [:password ]@]hostname [:port]
其中username 、password 、port参数为可选项。
hostname 既可以是域名也可以是 IP地址，如果设置
的是域名地址，则需要部署 DNS服务器。 port的取
值范围为 0～65535，超出范围按照默认端口 22处
理，仅在 SFTP服务器地址为 IPv4情况下支持配置端
口号。
说明
当hostname 为IP地址时，可不配置文件传输类型，此时
默认为 TFTP。
67 必选 设置中间文件名。中间文件的名称为 *.ini、*.py或
*.cfg。
●中间件文件名长度不要超过 64个字符，超过 64
字符时可能会导致文件下载失败。
●中间件文件名中不能包含特殊字符，如： &、
>、<、"或者'。
●中间文件名格式为： path/filename 。其中 path
可以是不包括文件服务器主机名的相对路径，如
“/script/ztp_script.py ”，也可以是包括服务器
主机名的绝对路径，如“ sftp://********/script/
ztp_script.py ”。若使用相对路径，则需要设置
Option 66 。
150 可选 设置TFTP服务器的 IP地址。
 HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 354
--------------------------------------------------

--- 第 79 页 ---

说明
ZTP通过DHCPv4申请的 IPv4地址租期至少为 1小时且不支持续租。
当待配置设备与 DHCPv4服务器不在同一网段时，需要配置 DHCP中继以转发 DHCP的交互报文。
以下配置步骤以路由器为例，如果选择其他类型设备作为 DHCPv4服务器或中继，请参
考相应的资料配置。
操作步骤
步骤1在作为 DHCPv4服务器的设备上配置地址池，详细配置请参见“配置地址池”，地址池
中Option编号： 6、66、67、150的配置请参见“（可选）配置 DHCPv4自定义选
项”。
步骤2配置地址池为接入用户分配地址，详细配置请参见“配置本地地址池为接入用户分配
地址示例”。
步骤3（可选）如果组网中存在 DHCP中继，在作为 DHCP中继的设备上进行配置，详细配置
请参见“配置 DHCP Relay ”。
----结束
******* 配置DHCPv6 Server 和DHCPv6 Relay
背景信息
需要运行 ZTP的设备在上电之前，须先部署 DHCPv6服务器，以确保作为 DHCP客户端
的空配置设备能正常获取到 IP地址、网关及中间文件服务器地址、中间文件名称等信
息。
运行ZTP的设备进入 DHCPv6 Solicit 阶段后，在发送 DHCPv6 Solicit 消息时会携带
DHCPv6 option 6 ，DHCPv6 option 6 用来携带客户端请求的选项代码。
DHCPv6服务器上需配置的 Options字段见表1-44。
表1-44 Options 字段说明
Option编号 是否可选 Option作用
5 必选 申请的 IA地址， IPv6地址以及生存期。
59 必选 中间文件路径，中间文件的名称为 *.ini、*.py或
*.cfg。中间文件名格式如下：
● tftp:// hostname /path/filename
● ftp://[ username [:password ]@]hostname /
path/filename
● sftp://[ username [:password ]@]hostname /
path/filename
 HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 355
--------------------------------------------------

--- 第 80 页 ---

说明
ZTP通过DHCPv6申请的 IPv6地址租期至少为 1小时且不支持续租。
当待配置设备与 DHCPv6服务器不在同一网段时，需要配置 DHCPv6中继以转发 DHCPv6的交互报
文。
操作步骤
步骤1在作为 DHCPv6服务器的设备上配置地址池，地址池中需要配置 Option 59 。
步骤2配置地址池为接入用户分配地址。
步骤3（可选）如果组网中存在 DHCPv6中继，在作为 DHCPv6中继的设备上进行配置，详细
配置请参见“配置 DHCPv6 Relay ”。
----结束
******* 配置文件服务器
背景信息
文件服务器用于存放空配置设备需要下载的文件，包括中间文件、版本文件等。用户
可以将路由器配置为文件服务器，但由于文件服务器需要占用设备的存储资源，因此
在使用路由器作为文件服务器时，需要考虑存储空间的问题。所以在 ZTP网络中，一般
需要部署第三方服务器，配置的具体方法请参见第三方服务器的操作指导。
用户可以将中间文件和版本文件部署在同一个文件服务器上。文件服务器可以是
TFTP/FTP/SFTP 服务器。
说明
文件服务器与空配置设备的缺省网关之间必须路由可达。
后续处理
配置完文件服务器后，将中间文件、版本文件存放至文件服务器。
说明
为充分保证文件服务器的安全，建议配置的文件服务器用户名唯一，并将其权限设置为只读，防
止被非法修改。 ZTP过程结束后，请关闭相应的文件服务器功能。
1.9.3.5 上电启动设备
背景信息
上述配置步骤完成后，将待配置设备上电启动。设备将自动下载版本文件并重新启
动，完成自动部署。
操作步骤
步骤1上电启动设备。
----结束HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 356
--------------------------------------------------
