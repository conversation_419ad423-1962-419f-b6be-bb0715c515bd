页码范围: 81 - 85


--- 第 81 页 ---

1.9.3.6 （可选）加载预配置脚本
背景信息
当设备空配置启动时，在进入 ZTP流程之前，如果需要对设备进行预配置命令下发，则
需要设置预配置脚本。
操作步骤
步骤1根据文件类型和格式要求编辑预配置脚本，文件格式请参见预配置脚本。
步骤2上传预配置脚本至主控板的存储介质中。
说明
本设备支持 FTP，TFTP以及SFTP上传文件，请参考 通过FTP访问其他设备的文件 ，通过TFTP访
问其他设备的文件 以及1.8.7 通过SFTP访问其他设备的文件 操作。请根据实际情况选择文件上传
方式上传到设备。
步骤3执行命令 set ztp pre-configuration  file-name ，加载预配置脚本。
若用户希望设备空配置启动时不执行 ZTP预配置流程，可以执行命令 reset ztp pre-
configuration ，清空预配置脚本。
步骤4执行命令 display ztp status ，可以查看当前设备预配置脚本的配置状态。
说明
在设备软件大包从低版本升级到当前版本时，若设置的启动配置文件为 vrpcfg.zip ，加载的预配
置脚本会被执行（若不想执行预配置脚本，可以通过执行命令 reset ztp pre-configuration 清空
预配置脚本）；当设置其它配置文件时，预配置脚本不会被执行。
----结束
******* （可选）配置自动补丁修复
通过配置 ZTP自动补丁修复，可以解决 ZTP开局自动部署时遇到的非环境问题（环境问
题如设备初始不稳定、服务器未启动等）。
背景信息
在ZTP开局自动部署过程中，出现非环境问题（环境问题如设备初始不稳定、服务器未
启动等）时，不需要工程师前往站点维修或设备返厂维修， ZTP支持自动补丁修复机
制。通过人工定位问题根因，联系工程师制作修复补丁， ZTP运行过程中可以自主识
别、设置修复补丁，并重启设备使修复补丁生效。设备重启后 ZTP会再次运行，由于此
时问题已被修复， ZTP可以顺利完成开局部署。
操作步骤
步骤1将修复补丁信息配置在 ZTP的中间文件中，配置详情见自动补丁修复机制。
步骤2将修复补丁上传到文件服务器中，并将修改后的 ZTP中间文件重新上传到文件服务器
中。
步骤3空配置重启设备， ZTP重新运行，识别中间文件中的修复补丁信息，自动完成修复过
程。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 357
--------------------------------------------------

--- 第 82 页 ---

说明
●修复过程中，在设置好修复补丁后，会立刻检测 ZTP是否使能，如果此时 ZTP去使能，则恢复
下次启动补丁为 ZTP开局前设备自带的原始补丁。
●修复完成后， ZTP设置了下次启动大包、补丁和配置文件，如果此时 ZTP去使能，则下次启动
补丁回退到修复补丁，不会回退为开局前设备自带补丁。
----结束
1.9.3.8 开启ZTP功能
背景信息
为了使设备空配置启动时能够自动执行 ZTP流程，需要开启设备的 ZTP功能。
操作步骤
步骤1执行命令 set ztp enable ，配置设备下次空配置启动时执行 ZTP流程。
若用户希望设备空配置启动时不执行 ZTP流程，也可以使用命令 set ztp disable 关闭设
备的ZTP功能。
步骤2执行命令 display ztp status ，可以查看设备下次空配置启动是否执行 ZTP流程。
----结束
1.9.3.9 检查配置结果
操作步骤
步骤1设备启动完成后，登录设备并通过命令 display startup 查看设备的启动文件是否与要
求的一致。
步骤2执行命令 display ztp status 查看设备是否是通过 ZTP完成部署。
步骤3如果设备没有完成自动配置，可以通过设备上保存的 ZTP日志查看出错原因。
设备执行 ZTP流程的信息会被保存在 cfcard:/ztp 目录下，文件名为： ztp_年月日时分
秒.log。
----结束
1.9.4 配置举例
介绍通过 DHCP实现自动部署的配置示例，示例中包括组网需求、配置注意事项和配置
思路等。
1.9.4.1 配置通过 DHCP实现ZTP自动部署示例
组网需求
如图1-80所示，某网络中新增两台空配置设备 RouterA和RouterB，连接到现网设备
RouterC上。RouterC作为RouterA和RouterB的出口网关。 RouterC与DHCP服务器、
文件服务器之间路由可达。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 358
--------------------------------------------------

--- 第 83 页 ---

用户希望空配置的 RouterA和RouterB在上电启动后能够自动加载相应的系统软件和配
置文件，完成开局部署，以降低现场配置的人力、时间成本。
图1-80 配置ZTP组网图
说明
本例中 interface1 ，interface2 分别表示 GE1/0/1，GE1/0/2。
配置思路
采用如下的思路配置：
1.配置文件服务器，将文件服务器作为 SFTP Server ，存放中间文件及系统软件、配
置文件。
说明
使用FTP协议存在安全风险，建议使用 SFTP进行文件传输。
2.编辑Python、ini或者cfg格式的中间文件，使不同的设备能够通过中间文件获取相
应的系统软件和配置文件。
3.配置DHCP服务器和中继，使空配置设备可以获得 DHCP服务器发送的 DHCP信
息。
4.将RouterA和RouterB上电，启动 ZTP流程。
操作步骤
步骤1配置文件服务器
●使用设备作为文件服务器，配置可参考 ******* 通过SFTP进行文件操作示例 。
●使用第三方服务器作为文件服务器，配置的具体方法请参见第三方服务器的操作
指导，设置 PC上SFTP的工作目录为 D:\ztp。文件服务器配置完成后，将设备需要
加载的系统软件和配置文件放在 D:\ztp目录下。
步骤2编辑中间文件
请按照 ******* 编辑中间文件 中的要求编辑中间文件，这里以 cfg为例，文件名称为
ztp_script.cfg ，内容请参见 cfg格式的中间文件。
中间文件编辑完成后存放至文件服务器的 D:\ztp目录下。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 359
--------------------------------------------------

--- 第 84 页 ---

步骤3配置DHCP服务器
# 配置DHCP服务器分配给客户端的 IP地址池，并参照 表1-45配置DHCP服务器 Option
选项的值。具体的配置方法请参见相应的产品资料对应 DHCP服务器配置相关章节。
表1-45 DHCP服务器 Option选项取值
Option编
号含义 取值
1 IP地址的子网掩码 *************
3 DHCP客户端的出口
网关********
67 文件服务器地址及中
间文件名sftp://client001:YsHsjx_202206@********/
ztp_script.cfg
 
# 设置DHCP服务器的 IP地址及网关，要求能够与 RouterA、RouterB的网关之间路由
可达。
步骤4配置DHCP中继
# 配置RouterC的DHCP中继功能，同时配置 RouterC与RouterA、RouterB相连的接口
IP地址为 ********，作为 RouterA、RouterB的缺省网关。
<HUAWEI> system-view
[~HUAWEI]  sysname RouterC
[*HUAWEI]  commit
[~RouterC]  interface GigabitEthernet 1/0/1
[~RouterC-GigabitEthernet1/0/1]  ip address ******** *************
[*RouterC-GigabitEthernet1/0/1]  undo shutdown
[*RouterC-GigabitEthernet1/0/1]  commit
[~RouterC-GigabitEthernet1/0/1]  quit
[~RouterC]  interface GigabitEthernet 1/0/2
[~RouterC-GigabitEthernet1/0/2]  ip address ******** *************
[*RouterC-GigabitEthernet1/0/2]  dhcp select relay
[*RouterC-GigabitEthernet1/0/2]  ip relay address ********
[*RouterC-GigabitEthernet1/0/2]  undo shutdown
[*RouterC-GigabitEthernet1/0/2]  commit
[~RouterC-GigabitEthernet1/0/2]  quit
步骤5将RouterA、RouterB上电，启动 ZTP流程
步骤6验证配置结果
# 设备启动完成后，可以登录到设备后执行命令 display startup 查看设备当前的系统
软件、配置文件是否与预期的一致。以 RouterA为例。
<RouterA> display startup
MainBoard:
  Configured  startup system software:        cfcard:/ V800R023C10SPC500B140_0424_new.cc
  Startup system software:                   cfcard:/ V800R023C10SPC500B140_0424_new.cc
  Next startup system software:              cfcard:/ V800R023C10SPC500B140_0424_new.cc
  Startup saved-configuration  file:          cfcard:/ vrpcfg.cfg
  Next startup saved-configuration  file:     cfcard:/ vrpcfg.cfg
  Startup paf file:                          default
  Next startup paf file:                     default
  Startup patch package:                     cfcard:/ NE40EV800R023C10SPC500.PAT
  Next startup patch package:                cfcard:/ NE40EV800R023C10SPC500.PAT
----结束HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 360
--------------------------------------------------

--- 第 85 页 ---

配置文件
ztp_script.cfg 文件
说明
下列文件中的 SHA256校验码只是举例，配置时以实际计算的值为准。
#sha256sum=" fffcd63f5e31f0891a0349686969969c1ee429dedeaf7726ed304f2d08ce1bc7 "
fileserver= sftp://username:password@hostname:port/path/ ;
mac= 00e0-fc12-3456 ;esn= 2102351931P0C3000154 ;devicetype= DEFAULT ;system-
version= V800R023C10SPC500 ;boot_python_file= V800R023C10SPC500.py ;system-
software= V800R023C10SPC500.cc ;system-config= V800R023C10SPC500.cfg ;system-
pat=V800R023C10SPC500SPH001.PAT ;
vrpcfg.cfg 文件
说明
下列文件以配置接口 IP和路由为例，实际使用中根据需要进行修改。
#
sysname HUAWEI
#
ip vpn-instance __LOCAL_OAM_VPN__
 ipv4-family
#
interface Ethernet0/0/0
 undo shutdown
 ip binding vpn-instance __LOCAL_OAM_VPN__
 ip address ************** *************
#
ip route-static vpn-instance __LOCAL_OAM_VPN__ 0.0.0.0 0.0.0.0 **************
#
RouterC 的配置文件
#
sysname RouterC
#
interface GigabitEthernet1/0/1
 undo shutdown
 ip address ******** *************
#
interface GigabitEthernet1/0/2
 undo shutdown
 ip address ******** *************
 dhcp select relay
 ip relay address ********
#
returnHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 361
--------------------------------------------------
