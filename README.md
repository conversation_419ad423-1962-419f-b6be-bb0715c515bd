# JSON转Excel工具

这个工具用于将JSON格式的题目数据转换为格式化的Excel文件。

## 功能特点

- 将JSON数据转换为Excel格式
- 自动格式化Excel表格（设置列宽、边框、字体等）
- 可选生成统计信息工作表（包含难度分布和答案分布图表）
- 支持自定义输入和输出文件路径
- 支持选择性导出指定列
- 完善的错误处理和提示信息

## 使用方法

### 基本用法

```bash
python json_to_excel.py
```

默认情况下，脚本会读取当前目录下的`题目综合.json`文件，并生成`题目综合.xlsx`文件。

### 命令行参数

- `-i, --input`：指定输入JSON文件路径
- `-o, --output`：指定输出Excel文件路径
- `-s, --stats`：添加统计信息工作表（包含难度分布和答案分布图表）
- `-c, --columns`：指定要包含的列，多个列名用空格分隔

### 示例

```bash
# 使用自定义输入和输出文件
python json_to_excel.py -i 我的题目.json -o 导出结果.xlsx

# 添加统计信息工作表
python json_to_excel.py -s

# 只导出指定列
python json_to_excel.py -c id question answer

# 组合使用多个参数
python json_to_excel.py -i 我的题目.json -o 导出结果.xlsx -s -c id question answer difficulty
```

### 查看可用列

直接运行脚本（不带参数）会显示默认JSON文件中可用的列名和使用示例：

```bash
python json_to_excel.py
```

## 输出说明

转换后的Excel文件包含以下内容：

1. **题目列表工作表**：包含所有题目数据，格式化后更易阅读
   - 自动调整列宽
   - 添加表格边框
   - 设置文本自动换行
   - 突出显示表头

2. **统计信息工作表**（使用`-s`参数时）：
   - 题目难度分布统计和图表
   - 答案选项分布统计和图表

## 错误处理

脚本包含完善的错误处理机制，可以处理以下情况：

- 输入文件不存在
- 输入文件不是有效的JSON格式
- 输出目录不存在（自动创建）
- 输出文件被占用或无写入权限
- 指定的列不存在

## 依赖库

- pandas：数据处理
- openpyxl：Excel文件操作
- argparse：命令行参数解析

## 安装依赖

```bash
pip install pandas openpyxl
```