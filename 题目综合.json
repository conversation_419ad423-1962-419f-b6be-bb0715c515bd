[{"id": 1, "question": "在NE40E V800R023C10SPC500中，使用ZTP自动部署时，DHCP Option 67 指定的中间文件名最大字符长度限制是多少？", "optionA": "32字符", "optionB": "64字符", "optionC": "128字符", "optionD": "无限制", "answer": "64字符", "reason": "手册*******节表1-43明确说明中间文件名长度不超过64字符，超过会导致下载失败。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 2, "question": "当通过STelnet登录NE40E时，若用户认证方式为DSA，SSH服务器端必须预先保存的客户端公钥类型是什么？", "optionA": "RSA公钥", "optionB": "DSA公钥", "optionC": "ECC公钥", "optionD": "SM2公钥", "answer": "DSA公钥", "reason": "********节步骤3要求服务器端保存客户端DSA公钥，并在步骤4绑定到用户。", "reference_pages": ["279", "281"], "difficulty": 2, "check": true}, {"id": 3, "question": "在SFTP配置示例中，SSH用户client002的授权目录被设置为哪个路径？", "optionA": "/home/<USER>/", "optionB": "cfcard:/", "optionC": "/etc/ssh/", "optionD": "/var/log/", "answer": "cfcard:/", "reason": "********节步骤6配置中明确指定client002的sftp-directory为cfcard:/。", "reference_pages": ["307"], "difficulty": 2, "check": true}, {"id": 4, "question": "ZTP自动部署时，若设备下次启动配置文件不为NULL或vrpcfg.zip，会导致什么结果？", "optionA": "ZTP会覆盖配置文件", "optionB": "ZTP不启动", "optionC": "设备进入恢复模式", "optionD": "自动生成新配置", "answer": "ZTP不启动", "reason": "1.9.2节特性限制表明确说明配置文件不符时ZTP不启动。", "reference_pages": ["351"], "difficulty": 2, "check": true}, {"id": 5, "question": "通过TFTP下载文件时，若目标文件已存在，系统默认的交互提示是什么？", "optionA": "直接覆盖", "optionB": "询问是否覆盖", "optionC": "跳过下载", "optionD": "报错退出", "answer": "询问是否覆盖", "reason": "********节步骤2示例显示系统提示\"overwrite? Please select [Y/N]\"", "reference_pages": ["299"], "difficulty": 2, "check": true}, {"id": 6, "question": "在SSH服务器配置中，dh-exchange的最小密钥长度被强制要求为多少位？", "optionA": "2048位", "optionB": "3072位", "optionC": "4096位", "optionD": "1024位", "answer": "3072位", "reason": "所有SSH配置示例中均包含\"ssh server dh-exchange min-len 3072\"参数。", "reference_pages": ["277", "285", "322"], "difficulty": 2, "check": true}, {"id": 7, "question": "使用SCP下载文件时，客户端必须使能哪个功能才能首次连接服务器？", "optionA": "ssh client first-time enable", "optionB": "scp server enable", "optionC": "ssh server-source", "optionD": "aaa authentication-mode", "answer": "ssh client first-time enable", "reason": "*********节步骤4明确要求首次登录需使能ssh client first-time enable。", "reference_pages": ["346"], "difficulty": 2, "check": true}, {"id": 8, "question": "在ZTP中间文件中，systemversion参数应该与哪个文件的实际版本保持一致？", "optionA": "补丁文件", "optionB": "系统软件包", "optionC": "配置文件", "optionD": "Python脚本", "answer": "系统软件包", "reason": "*******节示例中间文件中systemversion字段对应系统软件包版本V800R023C10SPC500。", "reference_pages": ["359"], "difficulty": 2, "check": true}, {"id": 9, "question": "配置SSH服务器支持非22端口访问时，修改监听端口的命令是什么？", "optionA": "ssh port 1025", "optionB": "ssh server port 1025", "optionC": "set ssh port 1025", "optionD": "ip ssh port 1025", "answer": "ssh server port 1025", "reason": "*********节步骤5明确使用\"ssh server port 1025\"命令修改端口。", "reference_pages": ["333"], "difficulty": 2, "check": true}, {"id": 10, "question": "在VPN场景下，PE设备通过STelnet访问CE时，必须附加哪个参数指定VPN实例？", "optionA": "-vpn-instance", "optionB": "-vrf", "optionC": "-instance", "optionD": "-vpn", "answer": "-vpn-instance", "reason": "*********节步骤8示例命令为\"stelnet ******** -vpn-instance vpn1\"。", "reference_pages": ["341"], "difficulty": 2, "check": true}, {"id": 11, "question": "ZTP过程中，DHCP服务器地址租期必须设置为至少多久？", "optionA": "30分钟", "optionB": "1小时", "optionC": "2小时", "optionD": "无限制", "answer": "1小时", "reason": "*******节说明ZTP申请的IPv4地址租期至少为1小时。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 12, "question": "当SSH用户认证方式为password时，必须配置的本地用户服务类型是什么？", "optionA": "ssh", "optionB": "telnet", "optionC": "ftp", "optionD": "http", "answer": "ssh", "reason": "********节说明password认证方式必须配置同名local-user的service-type ssh。", "reference_pages": ["279"], "difficulty": 2, "check": true}, {"id": 13, "question": "在SFTP配置中，SSH服务器默认支持的IPv4端口号是多少？", "optionA": "21", "optionB": "22", "optionC": "830", "optionD": "69", "answer": "22", "reason": "所有SSH配置示例中均显示SSH IPv4 server port为22。", "reference_pages": ["308"], "difficulty": 2, "check": true}, {"id": 14, "question": "使用ECC认证方式时，客户端生成的密钥模数默认值是多少？", "optionA": "256", "optionB": "384", "optionC": "521", "optionD": "1024", "answer": "521", "reason": "********节步骤1显示ECC密钥模数默认值为521。", "reference_pages": ["286"], "difficulty": 2, "check": true}, {"id": 15, "question": "ZTP自动部署时，若中间文件名包含特殊字符&会导致什么结果？", "optionA": "下载成功", "optionB": "文件被重命名", "optionC": "下载失败", "optionD": "自动转义字符", "answer": "下载失败", "reason": "*******节说明中间文件名不能包含&等特殊字符，否则会导致下载失败。", "reference_pages": ["352"], "difficulty": 2, "check": true}, {"id": 16, "question": "在STelnet RSA认证配置中，客户端公钥上传至服务器后，需要执行哪个命令绑定到用户？", "optionA": "ssh user assign rsa-key", "optionB": "ssh user bind-key", "optionC": "ssh user client002 assign rsa-key rsakey001", "optionD": "rsa-key bind user", "answer": "ssh user client002 assign rsa-key rsakey001", "reason": "1.8.14.2节步骤4明确使用\"ssh user client002 assign rsa-key rsakey001\"命令。", "reference_pages": ["277"], "difficulty": 2, "check": true}, {"id": 17, "question": "通过TFTP上传文件时，默认的传输模式是什么？", "optionA": "ASCII模式", "optionB": "二进制模式", "optionC": "压缩模式", "optionD": "流模式", "answer": "二进制模式", "reason": "********节步骤4显示TFTP默认以二进制模式(binary)传输文件。", "reference_pages": ["300"], "difficulty": 2, "check": true}, {"id": 18, "question": "在VPN场景SSH配置中，CE设备BGP进程的AS号是多少？", "optionA": "100", "optionB": "65410", "optionC": "200", "optionD": "300", "answer": "65410", "reason": "*********节步骤3配置CE1的BGP AS号为65410。", "reference_pages": ["337"], "difficulty": 2, "check": true}, {"id": 19, "question": "ZTP过程中，若需要预配置脚本，应将文件上传至设备的哪个目录？", "optionA": "cfcard:/", "optionB": "flash:/", "optionC": "/etc/ztp/", "optionD": "/tmp/", "answer": "cfcard:/", "reason": "1.9.3.6节步骤2要求将预配置脚本上传至主控板存储介质cfcard:/。", "reference_pages": ["356"], "difficulty": 2, "check": true}, {"id": 20, "question": "配置SSH服务器时，hmac算法被强制要求包含哪两种？", "optionA": "md5和sha1", "optionB": "sha2_512和sha2_256", "optionC": "sha1和sha256", "optionD": "md5和sha512", "answer": "sha2_512和sha2_256", "reason": "所有配置示例均包含\"ssh server hmac sha2_512 sha2_256\"参数。", "reference_pages": ["277", "285"], "difficulty": 2, "check": true}, {"id": 21, "question": "在STelnet DSA认证示例中，客户端生成的DSA密钥模数长度是多少？", "optionA": "1024位", "optionB": "2048位", "optionC": "3072位", "optionD": "4096位", "answer": "1024位", "reason": "********节查看DSA公钥显示Key modulus为1024。", "reference_pages": ["281"], "difficulty": 2, "check": true}, {"id": 22, "question": "通过HTTP下载证书时，客户端SSL策略绑定的PKI域名是什么？", "optionA": "domain1", "optionB": "pki1", "optionC": "ssl1", "optionD": "cert1", "answer": "domain1", "reason": "1.8.14.15节步骤1配置示例中PKI域名为domain1。", "reference_pages": ["348"], "difficulty": 2, "check": true}, {"id": 23, "question": "ZTP过程中，DHCP Option 66用于指定什么？", "optionA": "DNS服务器", "optionB": "中间文件名", "optionC": "文件服务器地址", "optionD": "子网掩码", "answer": "文件服务器地址", "reason": "*******节表1-43说明Option 66设置中间文件服务器主机名。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 24, "question": "在SFTP配置中，SSH用户client001的认证方式是什么？", "optionA": "RSA", "optionB": "DSA", "optionC": "password", "optionD": "ECC", "answer": "password", "reason": "********节步骤2说明client001认证方式为password。", "reference_pages": ["304"], "difficulty": 2, "check": true}, {"id": 25, "question": "配置SCP服务时，服务器端需要使能哪个功能？", "optionA": "sftp server enable", "optionB": "scp server enable", "optionC": "ssh server enable", "optionD": "ftp server enable", "answer": "scp server enable", "reason": "*********节步骤3明确使用\"scp server enable\"命令。", "reference_pages": ["346"], "difficulty": 2, "check": true}, {"id": 26, "question": "ZTP自动部署时，设备下次启动系统软件的文件名必须与哪个参数一致？", "optionA": "systemversion", "optionB": "boot_python_file", "optionC": "system_file", "optionD": "vrpcfg.cfg", "answer": "system_file", "reason": "*******节中间文件示例中system_file字段对应启动系统软件。", "reference_pages": ["359"], "difficulty": 2, "check": true}, {"id": 27, "question": "在VPN场景下，PE设备与CE建立BGP对等体时，必须配置的命令是什么？", "optionA": "peer x.x.x.x enable", "optionB": "peer x.x.x.x as-number", "optionC": "import-route direct", "optionD": "所有以上", "answer": "所有以上", "reason": "*********节步骤3配置中同时包含peer、as-number和import-route direct。", "reference_pages": ["337"], "difficulty": 2, "check": true}, {"id": 28, "question": "通过TFTP下载文件时，默认的块大小是多少字节？", "optionA": "512字节", "optionB": "1024字节", "optionC": "2048字节", "optionD": "65464字节", "answer": "512字节", "reason": "********节TFTP服务器信息显示default blksize为512。", "reference_pages": ["299"], "difficulty": 2, "check": true}, {"id": 29, "question": "配置SSH服务器时，key-exchange算法强制要求使用哪个？", "optionA": "dh_group14_sha1", "optionB": "dh_group_exchange_sha256", "optionC": "ecdh_sha2", "optionD": "rsa_sha2_512", "answer": "dh_group_exchange_sha256", "reason": "所有配置示例均包含\"ssh server key-exchange dh_group_exchange_sha256\"。", "reference_pages": ["277", "285"], "difficulty": 2, "check": true}, {"id": 30, "question": "ZTP过程中，若DHCP服务器未设置Option 67会导致什么结果？", "optionA": "使用默认文件名", "optionB": "ZTP退出", "optionC": "自动搜索文件", "optionD": "无限等待", "answer": "ZTP退出", "reason": "1.9.2节特性限制说明未设置Option 67会导致ZTP退出。", "reference_pages": ["352"], "difficulty": 2, "check": true}, {"id": 31, "question": "在STelnet ECC认证中，客户端生成的ECC密钥模数可选值不包括哪个？", "optionA": "256", "optionB": "384", "optionC": "512", "optionD": "521", "answer": "512", "reason": "********节步骤1显示ECC模数可选值为256、384、521，不包括512。", "reference_pages": ["286"], "difficulty": 2, "check": true}, {"id": 32, "question": "通过HTTP下载证书时，客户端必须配置的SSL策略名称是什么？", "optionA": "policy1", "optionB": "ssl1", "optionC": "cert1", "optionD": "http1", "answer": "policy1", "reason": "1.8.14.15节步骤1配置示例中SSL策略名称为policy1。", "reference_pages": ["348"], "difficulty": 2, "check": true}, {"id": 33, "question": "ZTP自动部署时，DHCP服务器地址租期不支持什么操作？", "optionA": "续租", "optionB": "释放", "optionC": "重新申请", "optionD": "老化", "answer": "续租", "reason": "*******节说明ZTP申请的地址租期至少1小时且不支持续租。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 34, "question": "在SFTP配置中，SSH服务器默认支持的IPv6端口号是多少？", "optionA": "21", "optionB": "22", "optionC": "830", "optionD": "69", "answer": "22", "reason": "所有SSH配置示例均显示SSH IPv6 server port为22。", "reference_pages": ["308"], "difficulty": 2, "check": true}, {"id": 35, "question": "配置SSH用户时，service-type为sftp表示什么权限？", "optionA": "仅SFTP访问", "optionB": "仅STelnet访问", "optionC": "所有SSH服务", "optionD": "仅FTP访问", "answer": "仅SFTP访问", "reason": "手册中service-type sftp明确限制为SFTP文件传输服务。", "reference_pages": ["307", "322"], "difficulty": 2, "check": true}, {"id": 36, "question": "ZTP过程中，若设备启动配置文件名为test.cfg而非vrpcfg.zip，会导致什么？", "optionA": "ZTP正常启动", "optionB": "ZTP不启动", "optionC": "自动重命名文件", "optionD": "配置文件被覆盖", "answer": "ZTP不启动", "reason": "1.9.2节特性限制说明配置文件必须为NULL或vrpcfg.zip，否则ZTP不启动。", "reference_pages": ["351"], "difficulty": 2, "check": true}, {"id": 37, "question": "在VPN场景下，PE设备ping CE时必须指定的参数是什么？", "optionA": "-vpn-instance", "optionB": "-a source-ip", "optionC": "-c count", "optionD": "-s size", "answer": "-a source-ip", "reason": "*********节说明ping时需指定-a source-ip-address参数。", "reference_pages": ["337"], "difficulty": 2, "check": true}, {"id": 38, "question": "通过SCP下载文件时，客户端源地址设置的命令是什么？", "optionA": "scp client-source -a", "optionB": "ssh client-source", "optionC": "ip scp source", "optionD": "set scp source", "answer": "scp client-source -a", "reason": "*********节步骤4使用\"scp client-source -a *******\"命令。", "reference_pages": ["346"], "difficulty": 2, "check": true}, {"id": 39, "question": "ZTP自动部署时，中间文件服务器可以是以下哪种类型？", "optionA": "仅TFTP", "optionB": "仅FTP", "optionC": "TFTP/FTP/SFTP", "optionD": "HTTP/HTTPS", "answer": "TFTP/FTP/SFTP", "reason": "*******节背景信息明确支持TFTP/FTP/SFTP服务器。", "reference_pages": ["355"], "difficulty": 2, "check": true}, {"id": 40, "question": "配置SSH服务器时，cipher算法强制要求包含哪些？", "optionA": "aes128_cbc", "optionB": "aes256_gcm aes128_gcm", "optionC": "3des_cbc", "optionD": "blowfish_cbc", "answer": "aes256_gcm aes128_gcm", "reason": "所有配置示例均包含\"ssh server cipher aes256_gcm aes128_gcm\"等参数。", "reference_pages": ["277", "285"], "difficulty": 2, "check": true}, {"id": 41, "question": "ZTP过程中，设备序列号通过DHCP哪个Option传递？", "optionA": "Option 60", "optionB": "Option 61", "optionC": "Option 66", "optionD": "Option 67", "answer": "Option 61", "reason": "*******节说明DHCP Option 61(Client-identifier)携带设备序列号。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 42, "question": "在STelnet配置中，VTY用户界面的认证模式必须设置为什么？", "optionA": "password", "optionB": "aaa", "optionC": "local", "optionD": "none", "answer": "aaa", "reason": "所有SSH配置示例中VTY界面均设置\"authentication-mode aaa\"。", "reference_pages": ["279", "304"], "difficulty": 2, "check": true}, {"id": 43, "question": "通过FTP访问设备时，本地用户的FTP目录设置为哪个路径？", "optionA": "/home/<USER>/", "optionB": "cfcard:/", "optionC": "/var/ftp/", "optionD": "/etc/ftp/", "answer": "cfcard:/", "reason": "1.8.14.7节步骤1配置示例中设置\"local-user huawei ftp-directory cfcard:/\"。", "reference_pages": ["301"], "difficulty": 2, "check": true}, {"id": 44, "question": "ZTP自动部署时，若DHCP服务器与设备不在同一网段，需要配置什么？", "optionA": "DNS服务器", "optionB": "DHCP中继", "optionC": "静态路由", "optionD": "默认网关", "answer": "DHCP中继", "reason": "*******节说明不在同一网段时需要配置DHCP中继转发报文。", "reference_pages": ["353"], "difficulty": 2, "check": true}, {"id": 45, "question": "配置SSH服务器时，publickey算法支持哪些类型？", "optionA": "仅RSA", "optionB": "RSA/DSA/ECC/SM2", "optionC": "仅ECC", "optionD": "仅DSA", "answer": "RSA/DSA/ECC/SM2", "reason": "手册中多次出现\"ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512\"等参数。", "reference_pages": ["277", "322"], "difficulty": 2, "check": true}, {"id": 46, "question": "ZTP过程中，修复补丁信息应配置在哪个文件中？", "optionA": "启动配置文件", "optionB": "中间文件", "optionC": "系统软件包", "optionD": "补丁文件本身", "answer": "中间文件", "reason": "1.9.3.7节步骤1说明将修复补丁信息配置在ZTP的中间文件中。", "reference_pages": ["356"], "difficulty": 2, "check": true}, {"id": 47, "question": "在VPN场景下，PE设备BGP配置的VPN-target值是多少？", "optionA": "100:1", "optionB": "111:1", "optionC": "200:1", "optionD": "65410:1", "answer": "111:1", "reason": "*********节步骤2配置中PE设备的vpn-target为111:1。", "reference_pages": ["336"], "difficulty": 2, "check": true}, {"id": 48, "question": "通过TFTP下载文件时，默认超时时间是多少秒？", "optionA": "3秒", "optionB": "5秒", "optionC": "10秒", "optionD": "30秒", "answer": "3秒", "reason": "********节TFTP服务器信息显示default timeout为3秒。", "reference_pages": ["299"], "difficulty": 2, "check": true}, {"id": 49, "question": "配置SSH用户时，authorization-type default root表示什么？", "optionA": "默认拒绝访问", "optionB": "默认授予root权限", "optionC": "默认使用本地用户", "optionD": "默认使用RADIUS认证", "answer": "默认授予root权限", "reason": "手册中authorization-type default root明确授予用户最高权限。", "reference_pages": ["277", "322"], "difficulty": 2, "check": true}, {"id": 50, "question": "ZTP自动部署完成后，如何验证设备是否通过ZTP部署？", "optionA": "display startup", "optionB": "display ztp status", "optionC": "display current-configuration", "optionD": "display version", "answer": "display ztp status", "reason": "*******节步骤2明确使用\"display ztp status\"查看是否通过ZTP完成部署。", "reference_pages": ["357"], "difficulty": 2, "check": true}, {"id": 1, "question": "一位工程师为NetEngine 40E配置了ZTP功能，通过DHCPv4为设备分发网络信息。为了让设备能够下载位于另一网段文件服务器上的`ztp_script.py`文件，DHCP服务器最核心、最基础需要提供的网络参数是什么？", "optionA": "Option 66 (TFTP服务器名)", "optionB": "Option 67 (启动文件名)", "optionC": "Option 3 (出口网关)", "optionD": "Option 6 (DNS服务器地址)", "answer": "Option 3 (出口网关)", "reason": "ZTP流程的第一步是网络可达。虽然Option 66和67指定了去哪里（服务器）和拿什么（文件），但如果设备没有网关地址（Option 3），它将无法将请求路由到文件服务器所在的网段。根据DHCPv4 Option表格，Option 3（出口网关）是“必选”的，是实现跨网段通信的基础。", "reference_pages": ["77-78"], "difficulty": 2, "check": false}, {"id": 2, "question": "在配置SSH用户公钥认证时，`ssh user [username] authentication-type [key-type]`和`ssh user [username] assign [key-type]-key [key-name]`这两条命令的核心功能区别是什么？", "optionA": "没有区别，它们是互为别名的命令。", "optionB": "`authentication-type`声明该用户采用何种认证方法，而`assign`则将该用户与一个具体的、已导入的公钥实体进行绑定。", "optionC": "`assign`命令用于在服务器上生成新密钥，而`authentication-type`用于指定其用途。", "optionD": "`authentication-type`是服务器端命令，`assign`是客户端命令。", "answer": "`authentication-type`声明该用户采用何种认证方法，而`assign`则将该用户与一个具体的、已导入的公钥实体进行绑定。", "reason": "通过观察RSA、DSA、ECC等多个公钥认证示例可以归纳出：`authentication-type`命令用于定义用户的登录策略（如设定为'rsa'或'dsa'），而`assign`命令则是认证策略的具体实现，它将用户账号与一个已经通过`peer-public-key`命令导入并命名的公钥关联起来，完成信任链的建立。", "reference_pages": ["1", "6", "13", "19"], "difficulty": 2, "check": true}, {"id": 3, "question": "管理员在“公网SSH客户端访问私网SSH服务器”场景中，发现PE1无法学习到CE1的私网路由。检查配置后，PE1上的BGP配置正确。根据示例，CE1上可能遗漏了哪项关键的BGP配置？", "optionA": "未在BGP进程下宣告直连路由", "optionB": "未配置正确的`peer as-number`", "optionC": "未在`ipv4-family unicast`视图下使能对等体", "optionD": "未配置`route-distinguisher`", "answer": "未在BGP进程下宣告直连路由", "reason": "在该MPLS VPN场景中，CE需要将其本地接口路由通告给PE。示例中，CE1的配置包含了`bgp 65410`和`import-route direct`命令。`import-route direct`的作用就是将设备上的直连路由引入到BGP中，从而发布给PE。如果缺少此配置，PE1将无法学习到CE1的私网路由********/24。", "reference_pages": ["62", "67"], "difficulty": 2, "check": true}, {"id": 4, "question": "在STelnet DSA认证示例中，当客户端`client002`尝试连接时，服务器端是如何验证其身份的？", "optionA": "通过比对客户端提供的密码与AAA中配置的密码。", "optionB": "通过使用导入的`dsakey001`公钥解密客户端用其私钥加密的数据。", "optionC": "通过检查客户端的源IP地址是否在ACL允许列表中。", "optionD": "通过要求客户端提供其主机名`client002`进行匹配。", "answer": "通过使用导入的`dsakey001`公钥解密客户端用其私钥加密的数据。", "reason": "DSA是一种非对称加密认证方式。整个示例的核心配置思路是：客户端生成自己的密钥对，将公钥传输给服务器并命名为`dsakey001`；服务器将用户`client002`与该公钥绑定。认证时，客户端用私钥签名，服务器用预存的公钥验签，从而确认身份。这体现了公钥认证的基本原理。", "reference_pages": ["3", "6"], "difficulty": 2, "check": true}, {"id": 5, "question": "一位工程师发现，在配置完STelnet后，`client001`使用密码可以登录，但`client002`使用ECC密钥认证失败，提示“Session is disconnected”。根据ECC认证示例，最可能的原因是忘记执行了哪个步骤？", "optionA": "忘记在服务器端执行 `stelnet server enable`", "optionB": "忘记为 `client002` 创建同名的 `local-user`", "optionC": "忘记执行 `ssh user client002 assign ecc-key ecckey001`", "optionD": "忘记在VTY下配置 `protocol inbound ssh`", "answer": "忘记执行 `ssh user client002 assign ecc-key ecckey001`", "reason": "在ECC认证方式中，仅创建用户并设置`authentication-type ecc`是不够的。必须通过`assign ecc-key`命令，将该用户与一个已导入的、具体的客户端公钥（示例中为ecckey001）进行绑定。如果缺少这一步，服务器不知道用哪个公钥去验证客户端，认证就会失败。而`local-user`的创建仅针对包含password的认证方式。", "reference_pages": ["11", "13"], "difficulty": 2, "check": true}, {"id": 6, "question": "根据“配置SSH服务器支持其他端口号访问”的示例，为什么攻击者使用标准端口22访问服务器会失败？", "optionA": "服务器上的防火墙拦截了22端口的流量。", "optionB": "服务器上没有配置任何用户，导致认证失败。", "optionC": "服务器的SSH服务监听端口已被改为1025，不再响应22端口的连接请求。", "optionD": "攻击者的客户端版本与服务器不兼容。", "answer": "服务器的SSH服务监听端口已被改为1025，不再响应22端口的连接请求。", "reason": "该示例的核心是通过`ssh server port 1025`命令修改SSH服务的监听端口，从而避免针对标准端口22的攻击。步骤7的验证结果明确显示，当客户端尝试用默认端口（即22）连接时，会收到“Error: Failed to connect to the server”的错误，这正是因为服务器进程已不在该端口上监听。", "reference_pages": ["54", "57-58"], "difficulty": 2, "check": false}, {"id": 7, "question": "在所有公钥认证示例（RSA/DSA/ECC/SM2）中，将客户端公钥导入服务器的操作，都必须进入哪个特定的配置视图来粘贴密钥代码？", "optionA": "AAA视图", "optionB": "对应加密算法的 `peer-public-key` 视图", "optionC": "PKI域视图", "optionD": "VTY用户界面视图", "answer": "对应加密算法的 `peer-public-key` 视图", "reason": "通过对比RSA、DSA、ECC、SM2四个认证示例，可以发现一个共同模式：导入公钥的第一步都是执行类似 `rsa peer-public-key [key-name]` 的命令，进入一个与加密算法同名的对等公钥视图，然后再进入`public-key-code`子视图粘贴密钥内容。例如`[*SSH Server-rsa-public-key]`或`[*SSH Server-dsa-public-key]`。", "reference_pages": ["1", "6", "13", "19"], "difficulty": 2, "check": true}, {"id": 8, "question": "在ZTP的配置限制中提到，如果DHCP地址租期过短会导致上线失败。这主要是因为ZTP流程中包含了哪些比较耗时的操作？", "optionA": "设备重启和生成本地密钥对", "optionB": "下载版本文件（如系统软件、补丁）并自动加载", "optionC": "与控制器进行心跳和状态上报", "optionD": "执行预配置脚本和格式化存储卡", "answer": "下载版本文件（如系统软件、补丁）并自动加载", "reason": "ZTP的目的是实现空配置设备的自动部署，核心任务是从文件服务器获取版本文件，包括系统软件（.cc）、配置文件（.cfg）、补丁文件（.pat）等。这些文件（特别是系统软件）可能很大，下载需要较长时间。如果在此期间DHCP租约到期且未成功续租，设备会失去IP地址，导致后续流程中断。因此，手册建议设置长周期租期。", "reference_pages": ["74", "75", "85"], "difficulty": 2, "check": true}, {"id": 9, "question": "在SFTP/STelnet场景中，如果一个SSH用户的认证方式被设置为`password-rsa`，根据手册中的说明，服务器端必须满足哪两个条件？", "optionA": "必须配置同名local-user，且服务器端应保存客户端的RSA公钥", "optionB": "必须配置同名local-user，且客户端必须使用RSA私钥", "optionC": "仅需配置同名local-user", "optionD": "仅需服务器端保存客户端的RSA公钥", "answer": "必须配置同名local-user，且服务器端应保存客户端的RSA公钥", "reason": "在多个配置章节（如STelnet DSA、STelnet ECC等）的“说明”部分都提到了SSH用户的认证方式规则。规则明确指出：当认证方式为`password`、`password-rsa`等包含密码的形式时，“必须配置同名的local-user用户”。同时，当认证方式为`RSA`、`password-rsa`等包含密钥的形式时，“服务器端应保存SSH客户端的RSA、DSA、SM2或ECC公钥”。因此`password-rsa`需要同时满足这两个条件。", "reference_pages": ["4", "11", "17", "29"], "difficulty": 2, "check": true}, {"id": 10, "question": "在“公网SSH客户端访问私网SSH服务器”示例中，PE1和PE2之间通过MP-BGP交换VPNv4路由。这种交换得以实现的前提是两台PE设备配置了共享的哪个属性？", "optionA": "相同的`route-distinguisher`", "optionB": "相同的`vpn-target`", "optionC": "相同的BGP AS号", "optionD": "相同的`lsr-id`", "answer": "相同的`vpn-target`", "reason": "在MPLS VPN中，`route-distinguisher`（RD）的作用是区分不同VPN的相同IP地址前缀，使其成为全局唯一的VPNv4路由。而`vpn-target`（VT）则是一种扩展团体属性，用于控制VPN路由的发布和接收。PE设备会导出带有特定Export VT的路由，并导入匹配其Import VT的路由。示例中PE1和PE2都配置了`vpn-target 111:1 both`，使得它们可以互相接收对方发布的属于该VPN的路由。", "reference_pages": ["61", "67-69"], "difficulty": 3, "check": false}, {"id": 11, "question": "在ZTP配置中，如果中间文件是一个Python脚本，那么该脚本相比ini或cfg文件，其主要优势在于什么？", "optionA": "文件体积更小，传输更快", "optionB": "语法更简单，易于编写", "optionC": "可以实现更复杂的、带逻辑判断的部署流程", "optionD": "安全性更高，无法被篡改", "answer": "可以实现更复杂的、带逻辑判断的部署流程", "reason": "手册在“编辑中间文件”步骤中提到：“其中，ini文件和cfg文件的使用要求低且配置简单，Python脚本文件对用户要求高”。这暗示了Python脚本的复杂性和能力。ini/cfg文件是声明式的键值对，只能定义静态的下载列表。而Python作为一种编程语言，可以在脚本中加入条件判断（如根据设备型号、内存大小选择不同文件）、循环、API调用等复杂逻辑，实现更灵活和智能的部署。", "reference_pages": ["77", "85"], "difficulty": 2, "check": true}, {"id": 12, "question": "管理员在客户端使用`scp client-source -a *******`命令的目的是什么？", "optionA": "指定SCP服务器的IP地址为*******", "optionB": "将文件上传到服务器的*******目录下", "optionC": "设置本次SCP传输使用的源IP地址为*******", "optionD": "限制只有IP地址为*******的服务器才能连接", "answer": "设置本次SCP传输使用的源IP地址为*******", "reason": "在SCP配置示例的步骤4中，明确注释了该命令的作用：“# 设置SCP客户端的源地址为LoopBack接口IP地址*******。”。当设备有多个接口和IP地址时，此命令用于指定发起SCP连接时使用的源IP，这在有严格路由策略或防火墙规则的网络环境中非常重要。", "reference_pages": ["71"], "difficulty": 2, "check": true}, {"id": 13, "question": "在SFTP RSA认证示例中，服务器和客户端生成密钥对时均选择了3072位模长。这个数值与哪个示例中DSA密钥对生成时提示的可选模长不一致？", "optionA": "SFTP DSA认证示例", "optionB": "STelnet DSA认证示例", "optionC": "STelnet ECC认证示例", "optionD": "STelnet SM2认证示例", "answer": "STelnet ECC认证示例", "reason": "SFTP RSA示例中，服务器和客户端生成密钥时，都提示模长范围是(2048, 3072)，并选择了3072。SFTP/STelnet的DSA示例中，提示可选模长为2048。而STelnet ECC示例中，提示可选模长为256、384、521。因此，3072这个值与ECC的可选模长列表不一致。本题考察对不同加密算法参数的区分能力。", "reference_pages": ["3", "11", "28", "30", "35", "37"], "difficulty": 3, "check": true}, {"id": 14, "question": "ZTPv4不支持QinQ接口的VLAN学习，这条特性限制适用于哪些NE40E产品型号？", "optionA": "仅NE40E-X16A和NetEngine 40E X8AK", "optionB": "仅NE40E-X8C和NE40E-X3A", "optionC": "所有在表格中列出的NE40E型号", "optionD": "仅NetEngine 40E X8AK", "answer": "所有在表格中列出的NE40E型号", "reason": "在“ZTP配置注意事项”的特性限制表格第一行，明确列出了“ZTPv4不支持QinQ接口VLAN学习”这条限制。其对应的“涉及产品”列包含了NE40E-X16C/X8C/X8A/X3A/X16A和NetEngine 40E X8AK，即手册中提到的所有型号。", "reference_pages": ["75"], "difficulty": 2, "check": true}, {"id": 15, "question": "在所有SFTP和STelnet的配置示例中，如果SSH用户需要通过密码进行认证，都必须在`aaa`视图下为该用户完成哪项配置？", "optionA": "配置`local-user [username] level 3`", "optionB": "配置`local-user [username] ftp-directory`", "optionC": "配置`local-user [username] password` 和 `service-type ssh`", "optionD": "配置`authentication-mode aaa`", "answer": "配置`local-user [username] password` 和 `service-type ssh`", "reason": "横向对比所有涉及密码认证的示例（如STelnet RSA/DSA/ECC/SM2中的client001），可以发现一个共同点：都在`aaa`视图下为`client001`配置了密码（`local-user client001 password ...`）和服务类型（`local-user client001 service-type ssh`）。这是使SSH服务能借用本地AAA用户数据库进行密码验证的基础。", "reference_pages": ["2", "4", "12", "18"], "difficulty": 2, "check": true}, {"id": 16, "question": "在FTP配置示例中，服务器通过`ftp server-source -i loopback 0`命令指定了服务源接口。这样做的主要目的是什么？", "optionA": "提高FTP传输速度", "optionB": "确保FTP服务使用一个稳定、始终在线的IP地址作为源地址，而不受物理接口状态变化的影响", "optionC": "为FTP服务启用加密功能", "optionD": "将FTP服务的监听范围限制在Loopback接口上", "answer": "确保FTP服务使用一个稳定、始终在线的IP地址作为源地址，而不受物理接口状态变化的影响", "reason": "在网络设备上，物理接口可能会因为链路故障等原因UP/DOWN，导致其IP地址不可用。而Loopback接口是逻辑接口，只要设备开机就始终处于UP状态。使用Loopback接口的IP作为服务的源地址，可以保证服务的稳定性和可达性，这是网络工程中的常见实践。示例中选择Loopback 0正是基于此原理。", "reference_pages": ["26-27"], "difficulty": 2, "check": false}, {"id": 17, "question": "在ZTP配置举例中，RouterC作为DHCP中继，其连接RouterA和RouterB的接口GE1/0/2上配置的`ip relay address`指向了谁？", "optionA": "文件服务器", "optionB": "DHCP服务器", "optionC": "RouterA", "optionD": "RouterC自身", "answer": "DHCP服务器", "reason": "DHCP中继（Relay）的作用是将客户端（RouterA/B）的DHCP广播请求单播转发给指定网段的DHCP服务器。在RouterC的配置文件中，`interface GigabitEthernet1/0/2`下明确配置了`ip relay address ********`。根据组网图，********是DHCP服务器的IP地址。因此，该命令的作用是告诉中继设备将DHCP请求转发到这个地址。", "reference_pages": ["83", "84"], "difficulty": 2, "check": true}, {"id": 18, "question": "为什么在进行ZTP部署时，手册建议为文件服务器的用户设置只读权限？", "optionA": "为了防止设备下载文件时发生写冲突", "optionB": "为了防止设备将本地日志上传到服务器，占用空间", "optionC": "为了充分保证文件服务器的安全，防止版本文件被非法修改", "optionD": "为了兼容仅支持只读操作的TFTP协议", "answer": "为了充分保证文件服务器的安全，防止版本文件被非法修改", "reason": "在“配置文件服务器”的“后续处理”部分，手册明确给出了建议：“为充分保证文件服务器的安全，建议配置的文件服务器用户名唯一，并将其权限设置为只读，防止被非法修改。”这直接解释了设置只读权限是出于安全考虑，以保证设备下载到的系统软件、配置文件等是可信的、未经篡改的。", "reference_pages": ["80"], "difficulty": 2, "check": true}, {"id": 19, "question": "在HTTP客户端配置示例中，通过`pki import-certificate ca domain domain1 filename test.crt`命令导入了一个证书。从命令结构和说明来看，`test.crt`扮演什么角色？", "optionA": "HTTP服务器的公钥", "optionB": "HTTP客户端的私钥", "optionC": "用于验证HTTP服务器证书的CA根证书", "optionD": "SSL策略文件", "answer": "用于验证HTTP服务器证书的CA根证书", "reason": "该命令中的`ca`关键字表示导入的是一个CA（Certificate Authority）证书。在SSL/TLS握手过程中，客户端需要使用预存的、受信任的CA根证书来验证服务器提供的证书是否是由该CA签发的，从而确认服务器的身份。因此，`test.crt`在这里是作为信任锚点（Trust Anchor）的CA证书。", "reference_pages": ["73"], "difficulty": 2, "check": true}, {"id": 20, "question": "在所有SFTP和STelnet示例中，`ssh client first-time enable`命令的作用是什么？", "optionA": "在客户端上启用SSH协议", "optionB": "允许客户端在首次连接未知服务器时，可以通过交互确认并保存服务器的公钥", "optionC": "强制客户端在首次连接时必须使用密码认证", "optionD": "为客户端生成一个临时的首次连接密钥", "answer": "允许客户端在首次连接未知服务器时，可以通过交互确认并保存服务器的公钥", "reason": "在首次连接一个SSH服务器时，客户端本地并没有保存该服务器的公钥，无法验证其身份。`ssh client first-time enable`命令启用了一个机制：当遇到这种情况时，客户端会提示用户“The server is not authenticated. Continue to access it?”，如果用户选择“y”，就可以将服务器的公钥保存在本地，以便后续连接时进行验证。这在多个示例的连接步骤中都有体现。", "reference_pages": ["2", "7", "13", "19", "32"], "difficulty": 2, "check": true}, {"id": 21, "question": "根据“ZTP配置注意事项”，在ZTP运行过程中，如果管理员执行了`set save configuration`命令，可能会导致什么后果？", "optionA": "ZTP流程立即退出", "optionB": "ZTP流程不受影响，但该配置不会被保存", "optionC": "影响ZTP正常运行，且用户配置可能失效", "optionD": "设备会立即重启以应用新配置", "answer": "影响ZTP正常运行，且用户配置可能失效", "reason": "在“ZTP配置注意事项”的特性限制表格中，明确指出：“ZTP运行过程中，禁止配置命令set save configuration、undo set save-configuration。否则：1、会影响ZTP正常运行 2、用户配置可能失效。”这直接回答了问题的后果。", "reference_pages": ["75"], "difficulty": 2, "check": true}, {"id": 22, "question": "在SFTP RSA认证示例中，`display ssh user-information`命令的输出显示`client002`的SFTP目录为空。然而，在配置步骤中，哪条命令本应为它指定目录？", "optionA": "`ssh user client002 sftp-directory cfcard:`", "optionB": "`local-user client002 ftp-directory cfcard:/`", "optionC": "`sftp server directory cfcard:`", "optionD": "`ssh authorization-type default root`", "answer": "`ssh user client002 sftp-directory cfcard:`", "reason": "该题目旨在考察配置意图与实际显示结果的差异。在SFTP RSA示例的步骤6“配置SSH用户的服务方式和授权目录”中，明确执行了`ssh user client002 sftp-directory cfcard:`命令。但在步骤8的验证输出中，该字段却显示为`-`。这表明尽管执行了配置命令，但在该示例的验证环节，结果并未如预期显示。", "reference_pages": ["31", "33"], "difficulty": 3, "check": true}, {"id": 23, "question": "在“公网SSH客户端访问私网SSH服务器”示例中，CE1和CE2的BGP AS号分别是多少？", "optionA": "都是100", "optionB": "CE1是65410，CE2是65420", "optionC": "CE1是100，CE2是200", "optionD": "都是65410", "answer": "CE1是65410，CE2是65420", "reason": "在示例的步骤3“在PE与CE之间建立EBGP对等体关系”中，配置CE1时使用了`bgp 65410`命令，配置CE2时使用了`bgp 65420`命令。这清晰地表明了两个CE设备位于不同的自治系统中。", "reference_pages": ["62"], "difficulty": 2, "check": true}, {"id": 24, "question": "在STelnet DSA认证示例中，服务器端的`display ssh user-information`命令输出显示`client002`的`User-public-key-name`为`dsakey001`。这个名称是在哪个配置步骤中被指定的？", "optionA": "步骤1，在服务器端生成本地密钥对时", "optionB": "步骤3，将客户端公钥导入服务器时", "optionC": "步骤3，在客户端生成本地密钥对时", "optionD": "步骤4，为SSH用户绑定公钥时", "answer": "步骤4，为SSH用户绑定公钥时", "reason": "在步骤3中，使用`dsa peer-public-key dsakey001 ...`命令将客户端公钥导入并命名为`dsakey001`。但在这一步，这个密钥还未与任何用户关联。在步骤4中，通过`ssh user client002 assign dsa-key dsakey001`命令，才正式将用户`client002`与名为`dsakey001`的公钥进行了绑定。`display`命令显示的是这种绑定关系。", "reference_pages": ["6", "8"], "difficulty": 2, "check": true}, {"id": 25, "question": "如果一台NetEngine 40E设备在ZTP过程中需要下载风河补丁（VxWorks Patch），根据手册的特性限制，ZTP流程会如何处理？", "optionA": "正常下载并加载补丁", "optionB": "跳过该补丁，继续其他流程", "optionC": "ZTP不支持风河补丁，可能导致流程异常或失败", "optionD": "提示用户手动确认安装", "answer": "ZTP不支持风河补丁，可能导致流程异常或失败", "reason": "“ZTP配置注意事项”的特性限制表格中明确有一条：“ZTP(SZTP)不支持风河补丁,使用ZTP(SZTP)开局升级不要使用风河补丁。” 这清晰地说明了ZTP对这类补丁的不支持，尝试使用会导致问题。", "reference_pages": ["76"], "difficulty": 2, "check": true}, {"id": 26, "question": "在TFTP配置示例中，客户端下载文件时，命令中指定的本地目标文件名是什么？", "optionA": "a.txt", "optionB": "b.txt", "optionC": "sample.txt", "optionD": "V800R023C10SPC500B020D0123.cc", "answer": "b.txt", "reason": "示例的步骤2中，客户端执行的下载命令是 `tftp ************ get a.txt cfcard:/b.txt`。该命令的结构是 `tftp [服务器IP] get [源文件名] [本地目标路径/文件名]`。因此，`a.txt`是源文件名，而`b.txt`是保存在本地的目标文件名。", "reference_pages": ["24"], "difficulty": 2, "check": true}, {"id": 27, "question": "在所有配置示例中，启用SSH服务（无论是用于STelnet, SFTP还是SCP）都需要在`user-interface vty 0 4`视图下执行哪条命令？", "optionA": "`authentication-mode aaa`", "optionB": "`protocol inbound ssh`", "optionC": "`user privilege level 3`", "optionD": "`ssh client first-time enable`", "answer": "`protocol inbound ssh`", "reason": "横向比较所有示例的配置文件，可以发现`user-interface vty 0 4`的配置是标准模板。其中，`protocol inbound ssh`命令的作用是允许VTY线路接受SSH协议的连接。没有这条命令，即使用户和SSH服务本身都配置好了，外部连接也无法进入。`authentication-mode`和`user privilege level`是认证和权限相关的，但`protocol inbound`是协议准入的开关。", "reference_pages": ["2", "4", "9", "11", "15", "17"], "difficulty": 2, "check": true}, {"id": 28, "question": "根据“ZTP配置举例”的组网图和配置，RouterA/B、RouterC和DHCP服务器分别位于哪个IP网段？", "optionA": "都在********/24网段", "optionB": "RouterA/B在********/24，RouterC在********/24，DHCP服务器在********/24", "optionC": "RouterA/B和RouterC的GE1/0/2在********/24，RouterC的GE1/0/1在********/24，DHCP服务器在********/24", "optionD": "RouterA/B在********/24，RouterC和DHCP服务器在********/24", "answer": "RouterA/B和RouterC的GE1/0/2在********/24，RouterC的GE1/0/1在********/24，DHCP服务器在********/24", "reason": "这需要综合分析组网图、RouterC的配置文件和其作为中继的配置。RouterC的GE1/0/2口IP为********/24，作为RouterA/B的网关，因此A/B在此网段。RouterC的`ip relay address`是********，这是DHCP服务器的地址。RouterC自身的另一个接口GE1/0/1的IP是********/24，用于连接核心网络。这需要整合图、文、配置才能得出完整的网络拓扑。", "reference_pages": ["83", "84-85"], "difficulty": 3, "check": false}, {"id": 29, "question": "在STelnet/SFTP的SM2认证示例中，客户端和服务器端都需要执行`sm2 key-pair label sm2key001`来生成密钥。这条命令与其他公钥算法的密钥生成命令（如`rsa local-key-pair create`）最显著的区别是什么？", "optionA": "SM2命令需要指定一个`label`，而其他算法不需要。", "optionB": "SM2命令不支持指定模长。", "optionC": "SM2命令只能在客户端执行。", "optionD": "SM2命令不会有“generation will take a short while”的提示。", "answer": "SM2命令需要指定一个`label`，而其他算法不需要。", "reason": "对比SM2与其他算法的密钥生成步骤，可以发现RSA/DSA/ECC都使用`[算法] local-key-pair create`命令，系统会自动生成一个基于主机名的默认密钥名。而SM2则使用`sm2 key-pair label [自定义名称]`的格式，强制用户为密钥指定一个“标签”（label），这个标签在后续分配密钥时会用到。", "reference_pages": ["3", "11", "18", "28", "30", "35"], "difficulty": 2, "check": true}, {"id": 30, "question": "在“公网SSH客户端访问私网SSH服务器”示例中，CE1上配置了`ssh user client002 service-type sftp`和`sftp-directory cfcard:`。这些配置的最终目的是什么？", "optionA": "允许`client002`以sftp方式登录，并将其操作限制在`cfcard:`目录下", "optionB": "将CE1配置成一个SFTP服务器，根目录为`cfcard:`", "optionC": "允许`client002`将文件从`cfcard:`上传到PE1", "optionD": "为`client002`在`cfcard:`下创建一个家目录", "answer": "允许`client002`以sftp方式登录，并将其操作限制在`cfcard:`目录下", "reason": "`service-type sftp`命令授权用户`client002`使用SFTP服务。`sftp-directory cfcard:`命令则为该用户的SFTP会话指定了一个根目录（chroot jail），用户登录后将处于`cfcard:`目录中，且无法访问其上级目录。这是一种常见的安全增强措施，用于限制用户的活动范围。", "reference_pages": ["65", "67"], "difficulty": 2, "check": true}, {"id": 31, "question": "在STelnet DSA认证示例中，当`client002`登录时，在输入用户名后，为何系统会提示“Enter password:”，尽管其认证方式是DSA？", "optionA": "这是一个回退机制，如果密钥认证失败，则尝试密码认证。", "optionB": "这是用于解锁私钥的密码（passphrase）。", "optionC": "这是一个系统BUG，该提示不应出现。", "optionD": "在登录成功后，系统提示用户修改初始密码，这是修改密码的流程。", "answer": "在登录成功后，系统提示用户修改初始密码，这是修改密码的流程。", "reason": "仔细观察`client002`的登录流程，在输入用户名和选择密钥类型后，紧接着的提示是“Warning: The initial password poses security risks. The password needs to be changed. Change now? [Y/N]:n”，这表明之前的密钥认证已经成功，系统进入了登录后的处理流程。此时的“Enter password:”提示是与修改初始密码相关的，而非认证环节的一部分。这需要理解SSH登录后的交互流程。", "reference_pages": ["7"], "difficulty": 3, "check": true}, {"id": 32, "question": "在ZTP配置中，如果需要加载一个预配置脚本，该脚本文件应该被上传到待配置设备的哪个位置？", "optionA": "DHCP服务器上", "optionB": "文件服务器的根目录", "optionC": "待配置设备主控板的存储介质中", "optionD": "任意与设备相连的U盘中", "answer": "待配置设备主控板的存储介质中", "reason": "在“（可选）加载预配置脚本”章节中，步骤2明确指出：“上传预配置脚本至主控板的存储介质中。”这表明预配置脚本是在ZTP流程开始前就存在于本地设备上的，而不是像版本文件一样从远程服务器下载的。它用于在设备进行网络初始化之前执行一些基础配置。", "reference_pages": ["81"], "difficulty": 2, "check": true}, {"id": 33, "question": "在SCP配置示例中，为`client001`配置的`service-type`是`all`。这个设置意味着什么？", "optionA": "允许`client001`使用所有类型的加密算法。", "optionB": "允许`client001`访问服务器上的所有文件目录。", "optionC": "允许`client001`使用SSH支持的所有服务类型，如stelnet、sftp、scp等。", "optionD": "允许`client001`从任何源IP地址登录。", "answer": "允许`client001`使用SSH支持的所有服务类型，如stelnet、sftp、scp等。", "reason": "在SSH用户配置中，`service-type`参数用于指定该用户被授权使用哪种SSH子服务。其他示例中明确指定了`stelnet`或`sftp`。而`all`是一个通配符，表示该用户可以使用所有当前设备上已启用的SSH服务，包括STelnet、SFTP、SCP等。这在SCP示例的步骤2和最终配置文件中都有体现。", "reference_pages": ["71", "72"], "difficulty": 2, "check": true}, {"id": 34, "question": "在SFTP ECC认证示例中，当客户端`client002`执行`display ecc local-key-pair public`时，输出的密钥类型（Key Type）是什么？", "optionA": "ECC Encryption Key", "optionB": "ECDSA Key", "optionC": "ECC Public Key", "optionD": "NISTP521 Key", "answer": "ECC Encryption Key", "reason": "在SFTP ECC认证示例的步骤3，客户端查看公钥的命令`display ecc local-key-pair public`的输出中，明确有一行显示`Key Type : ECC Encryption Key`。这是一个直接考察命令输出细节的问题，但需要定位到正确的示例和步骤。", "reference_pages": ["44"], "difficulty": 2, "check": true}, {"id": 35, "question": "在“配置SSH服务器支持其他端口号访问”示例中，`ssh server-source -i loopback 0`命令与`ssh server port 1025`命令共同实现了什么效果？", "optionA": "只允许从Loopback 0接口以1025端口访问SSH服务。", "optionB": "将SSH服务的监听地址绑定到Loopback 0的IP上，并且监听端口设置为1025。", "optionC": "指定SSH服务向外发起连接时使用Loopback 0的IP，但入站监听端口为1025。", "optionD": "为Loopback 0接口单独创建一个监听在1025端口的SSH实例。", "answer": "指定SSH服务向外发起连接时使用Loopback 0的IP，但入站监听端口为1025。", "reason": "`ssh server-source`命令用于指定SSH服务器作为客户端（例如在进行Server-to-Server传输或某些特定功能时）发起连接时使用的源IP地址，是为了保证源地址的稳定性。而`ssh server port`命令则指定了SSH服务器作为服务端时监听的入站端口。两者功能独立，共同定义了SSH服务的出入站行为。", "reference_pages": ["57", "59"], "difficulty": 3, "check": true}, {"id": 36, "question": "在ZTP配置举例的`ztp_script.cfg`文件中，`sha256sum`字段的作用是什么？", "optionA": "作为该中间文件本身的校验码", "optionB": "作为即将下载的系统软件的校验码", "optionC": "作为设备ESN的哈希值", "optionD": "作为连接文件服务器的密码", "answer": "作为该中间文件本身的校验码", "reason": "在`ztp_script.cfg`文件示例中，`sha256sum`位于文件的第一行注释中。这是一种安全机制，设备下载中间文件后，会计算文件的SHA256值并与该字段进行比对，以确保中间文件在传输过程中没有被篡改。这保证了后续的下载列表和指令是可信的。", "reference_pages": ["85"], "difficulty": 2, "check": true}, {"id": 37, "question": "在STelnet DSA认证示例中，当服务器生成本地密钥对时，系统提示`The key modulus can be any one of the following: 2048.`。这与SFTP DSA认证示例中服务器生成密钥时的提示有何不同？", "optionA": "完全相同", "optionB": "SFTP示例中提示的是1024", "optionC": "SFTP示例中提示的是3072", "optionD": "SFTP示例中没有模长提示", "answer": "完全相同", "reason": "在STelnet DSA认证示例步骤1中，服务器执行`dsa local-key-pair create`后，提示`Info: The key modulus can be any one of the following: 2048.`。在SFTP DSA认证示例步骤1中，执行相同的命令后，提示也是`Info: The key modulus can be any one of the following : 2048.`。两个示例中的提示信息是一致的。", "reference_pages": ["3", "35"], "difficulty": 2, "check": true}, {"id": 38, "question": "在ZTP特性限制中，提到“同一台设备不可同时和两台及以上DHCP服务器在一个广播域中”。这主要是为了避免什么问题？", "optionA": "设备收到多个冲突的IP地址", "optionB": "设备收到多个不同的中间文件路径，导致ZTP功能失效", "optionC": "网络中产生广播风暴", "optionD": "DHCP服务器的CPU负载过高", "answer": "设备收到多个不同的中间文件路径，导致ZTP功能失效", "reason": "设备发出一个DHCP Discover广播，如果广播域内有多个DHCP服务器，它可能会收到多个DHCP Offer。这些Offer可能来自不同的管理域，包含不同的网关、文件服务器地址和中间文件名。设备无法确定应采用哪个Offer，这种不确定性会导致ZTP流程无法继续，因此手册中直接将其定义为“功能失效”。", "reference_pages": ["75"], "difficulty": 2, "check": true}, {"id": 39, "question": "在“公网SSH客户端访问私网SSH服务器”示例中，PE1上执行`display bgp vpnv4 vpn-instance vpn1 peer`命令，可以看到与CE1的对等体关系已建立。输出中的`PrefRcv`（已接收前缀数）为1。这个前缀具体指什么？", "optionA": "CE1的Loopback0地址", "optionB": "CE1连接PE1的接口地址", "optionC": "CE1所在的整个65410 AS的路由", "optionD": "CE1上`import-route direct`引入的直连网段路由", "answer": "CE1上`import-route direct`引入的直连网段路由", "reason": "在该BGP配置中，CE1上配置了`import-route direct`，这会将CE1的直连路由（即GE1/0/1接口所在的********/24网段）引入BGP并通告给PE1。因此，PE1从CE1接收到的这个前缀就是********/24。CE1的Loopback0地址（********/32）虽然存在，但也属于直连路由的一部分被引入。", "reference_pages": ["62", "67"], "difficulty": 3, "check": true}, {"id": 40, "question": "对比SFTP RSA认证示例和SFTP ECC认证示例，在服务器端为用户`client002`授权SFTP目录时，使用的命令有何细微差别？", "optionA": "没有差别，命令完全一样", "optionB": "RSA示例使用的是`sftp-directory cfcard:`，ECC示例使用的是`sftp-directory cfcard：`（使用了中文冒号）", "optionC": "RSA示例使用的是绝对路径，ECC示例使用的是相对路径", "optionD": "RSA示例中没有配置该命令", "answer": "RSA示例使用的是`sftp-directory cfcard:`，ECC示例使用的是`sftp-directory cfcard：`（使用了中文冒号）", "reason": "这是一个考察文本细节的问题，但也反映了文档编写中可能出现的不一致性。在SFTP RSA示例步骤6中，命令为`ssh user client002 sftp-directory cfcard:`。而在SFTP ECC示例步骤6中，命令显示为`ssh user client002 sftp-directory cfcard：`。后者使用了全角（中文）冒号，这在实际配置中是无效的，但题目考察的是手册中的文本内容。", "reference_pages": ["31", "45"], "difficulty": 3, "check": true}, {"id": 41, "question": "在`display ssh server status`的输出中，`SSH version 1.x compatibility`默认是启用还是禁用？", "optionA": "Enable", "optionB": "Disable", "optionC": "Auto", "optionD": "未显示该项", "answer": "Enable", "reason": "在STelnet DSA、SFTP RSA等多个示例的验证步骤中，`display ssh server status`的输出都一致地显示`SSH version 1.x compatibility : Enable`。这表明为了兼容旧客户端，系统默认开启了对SSHv1的兼容。", "reference_pages": ["8", "14", "20", "32"], "difficulty": 2, "check": true}, {"id": 42, "question": "在ZTP配置中，如果中间文件和版本文件重名，例如设备下次启动文件已是`V800R023C10SPC500.cc`，而中间文件中指定的`system_software`也是`V800R023C10SPC500.cc`，将会发生什么？", "optionA": "设备会重新下载并覆盖现有文件", "optionB": "ZTP不会下载重名的文件", "optionC": "ZTP流程报错并退出", "optionD": "设备会提示用户确认是否覆盖", "answer": "ZTP不会下载重名的文件", "reason": "“ZTP配置注意事项”的特性限制表格中明确规定：“中间件中设置的待下载的CC包、配置文件、补丁文件等不能与设备上运行或下次启动设置的CC包、配置文件、补丁文件重名。ZTP不会下载与设备上运行的或下次启动设置的文件重名的文件。”这是一种优化机制，避免不必要的下载。", "reference_pages": ["76"], "difficulty": 2, "check": true}, {"id": 43, "question": "在STelnet ECC认证示例中，当服务器端创建`ecc local-key-pair`时，系统提示了三种可选的模长，但示例中通过交互输入最终选择了哪一个？", "optionA": "256", "optionB": "384", "optionC": "521", "optionD": "2048", "answer": "521", "reason": "在该示例的步骤1中，服务器创建密钥的交互过程显示：`Info: The key modulus can be any one of the following: 256, 384, 521.`以及`Please input the modulus [default=521]:521`。这表明示例中手动输入了521（尽管它也是默认值）。", "reference_pages": ["11"], "difficulty": 2, "check": true}, {"id": 44, "question": "在所有STelnet示例的最终服务器配置文件中，`ssh server hmac`命令配置了哪两种HMAC算法？", "optionA": "`sha1`和`sha2_256`", "optionB": "`sha2_512`和`sha2_256`", "optionC": "`md5`和`sha1`", "optionD": "`sha2_512`和`sha1`", "answer": "`sha2_512`和`sha2_256`", "reason": "通过查阅STelnet RSA、DSA、ECC、SM2四个示例的最终服务器配置文件，可以发现在每个配置文件中都存在一行`ssh server hmac sha2_512 sha2_256`。这是一个在所有示例中保持一致的安全配置。", "reference_pages": ["1", "9", "15", "21"], "difficulty": 2, "check": true}, {"id": 45, "question": "在FTP配置示例中，登录FTP服务器的用户`huawei`的权限级别被设置为了多少？", "optionA": "1", "optionB": "3", "optionC": "15", "optionD": "未设置", "answer": "3", "reason": "在FTP示例的步骤1中，服务器配置部分明确执行了`local-user huawei level 3`命令。在其最终的配置文件中，也包含了这一行，明确了用户的权限级别。", "reference_pages": ["26-27"], "difficulty": 2, "check": false}, {"id": 46, "question": "在DHCPv4的ZTP场景下，如果中间文件服务器是FTP类型，并且需要用户名和密码认证，那么Option 66的正确格式应该是什么？", "optionA": "`ftp://********/files/`", "optionB": "`ftp://username:password@********`", "optionC": "`********;username;password`", "optionD": "`tftp://********`", "answer": "`ftp://username:password@********`", "reason": "“DHCPv4 Server和DHCPv4 Relay”的Option表格中，对Option 66的格式有详细说明，其中FTP的格式为`ftp://[username[:password]@]hostname`。选项B完全符合这个带有用户名和密码的格式规范。", "reference_pages": ["78"], "difficulty": 2, "check": true}, {"id": 47, "question": "在“配置SSH服务器支持其他端口号访问”示例中，`display ssh server status`命令显示`SSH server keepalive`的状态是什么？", "optionA": "Enable", "optionB": "Disable", "optionC": "60", "optionD": "未显示该项", "answer": "Disable", "reason": "在该示例的步骤7“检查配置结果”中，`display ssh server status`的输出清晰地显示了一行`SSH server keepalive : Disable`。这表明在该配置场景下，SSH的keepalive功能是默认关闭的。", "reference_pages": ["58"], "difficulty": 2, "check": true}, {"id": 48, "question": "在ZTP配置中，如果设备启动时，检测到预配置脚本存在，ZTP流程会如何执行？", "optionA": "先执行预配置脚本，再进入网络初始化和文件下载流程", "optionB": "先完成网络初始化和文件下载，最后执行预配置脚本", "optionC": "跳过ZTP流程，只执行预配置脚本", "optionD": "忽略预配置脚本，直接进入ZTP网络初始化", "answer": "先执行预配置脚本，再进入网络初始化和文件下载流程", "reason": "“（可选）加载预配置脚本”的背景信息中描述道：“当设备空配置启动时，在进入ZTP流程之前，如果需要对设备进行预配置命令下发，则需要设置预配置脚本。” 这明确了预配置脚本的执行时机是在完整的ZTP网络流程开始之前。", "reference_pages": ["81"], "difficulty": 2, "check": true}, {"id": 49, "question": "在“公网SSH客户端访问私网SSH服务器”示例中，`ssh server dh-exchange min-len`参数在CE1的最终配置文件中被设置成了什么值？", "optionA": "2048", "optionB": "1024", "optionC": "3072", "optionD": "未配置", "answer": "3072", "reason": "在该示例的CE1最终配置文件中，可以找到一行明确的配置：`ssh server dh-exchange min-len 3072`。这个参数用于增强Diffie-Hellman密钥交换的安全性，通过设置一个最小的密钥长度。", "reference_pages": ["67"], "difficulty": 2, "check": true}, {"id": 50, "question": "在`ztp_script.cfg`示例文件中，条目`mac=00e0-fc12-3456;esn=...;devicetype=DEFAULT;...`揭示了ZTP中间文件的何种匹配逻辑？", "optionA": "必须同时满足MAC、ESN和设备类型才能匹配", "optionB": "优先根据MAC地址匹配，若不匹配则尝试ESN，最后使用DEFAULT作为通配", "optionC": "仅使用devicetype进行匹配", "optionD": "随机选择一个条件进行匹配", "answer": "优先根据MAC地址匹配，若不匹配则尝试ESN，最后使用DEFAULT作为通配", "reason": "虽然手册没有明确说明匹配的优先级，但这种`[specific-id-1];[specific-id-2];[generic-id]`的结构在配置文件中通常遵循从最具体到最通用的匹配顺序。设备会先尝试用自己唯一的MAC地址去匹配，如果找不到对应的条目，再用自己的ESN去匹配。如果都找不到，最后会匹配`devicetype=DEFAULT`这样的通用条目。这是从配置文件结构推断出的最合理的逻辑。", "reference_pages": ["85"], "difficulty": 3, "check": true}, {"id": 1, "question": "某网络管理员需要配置SSH服务器支持多种认证方式，在查看当前支持的用户认证类型时，以下哪种认证方式组合是不正确的？", "optionA": "password-rsa, password-dsa", "optionB": "password-ecc, password-sm2", "optionC": "password-x509v3-rsa, all", "optionD": "password-md5, password-sha1", "answer": "D", "reason": "根据手册内容，SSH服务器支持的用户认证类型包括RSA、password-rsa、DSA、password-dsa、SM2、password-sm2、ECC、password-ecc、x509v3-ssh-rsa、password-x509v3-rsa和all，但不包括password-md5和password-sha1这两种认证方式。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 2, "question": "网络工程师在配置SSH用户密码时，系统提示密码不符合要求。根据设备要求，以下哪个密码设置是合法的？", "optionA": "<PERSON><PERSON><PERSON>@123", "optionB": "huawei123", "optionC": "Aa123", "optionD": "\"Aa 123\"45\"", "answer": "A", "reason": "根据手册要求，设置的密码必须满足：密码长度范围是8～16，至少包含两种类型字符（大写字母、小写字母、数字及特殊字符）。特殊字符不包括\"？\"和空格。选项A符合要求，而B长度足够但只有小写字母和数字，C长度不足，D虽然使用双引号可以在密码中间输入空格，但格式不正确（\"Aa 123\"45\"不是合法密码）。", "reference_pages": ["11-20"], "difficulty": 2, "check": false}, {"id": 3, "question": "在配置SSH服务器时，管理员需要设置更安全的加密算法、HMAC算法和密钥交换算法，以下哪个配置组合最符合安全建议？", "optionA": "ssh server cipher aes128_cbc; ssh server hmac sha1; ssh server key-exchange dh_group1_sha1", "optionB": "ssh server cipher aes256_gcm; ssh server hmac sha2_512; ssh server key-exchange dh_group_exchange_sha256", "optionC": "ssh server cipher 3des_cbc; ssh server hmac md5; ssh server key-exchange dh_group14_sha1", "optionD": "ssh server cipher aes128_ctr; ssh server hmac sha1_96; ssh server key-exchange dh_group_exchange_sha1", "answer": "B", "reason": "根据手册中的配置示例和安全建议，选项B使用了最安全的加密算法(aes256_gcm)、HMAC算法(sha2_512)和密钥交换算法(dh_group_exchange_sha256)组合，这与手册中推荐的安全配置一致。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 4, "question": "网络管理员在配置SSH服务器的DH密钥交换最小长度时，以下哪个配置是符合手册建议的？", "optionA": "ssh server dh-exchange min-len 1024", "optionB": "ssh server dh-exchange min-len 2048", "optionC": "ssh server dh-exchange min-len 3072", "optionD": "ssh server dh-exchange min-len 4096", "answer": "C", "reason": "根据手册中的配置示例，推荐的SSH服务器DH密钥交换最小长度配置是'ssh server dh-exchange min-len 3072'，这体现在多个配置示例中。", "reference_pages": ["1-10", "41-50"], "difficulty": 2, "check": false}, {"id": 5, "question": "在SFTP访问配置中，如果需要使用ECC认证方式，客户端需要执行哪个命令来生成本地密钥对？", "optionA": "rsa local-key-pair create", "optionB": "dsa local-key-pair create", "optionC": "ecc local-key-pair create", "optionD": "sm2 local-key-pair create", "answer": "C", "reason": "根据手册中SFTP访问其他设备文件配置示例（ECC认证方式）部分，当使用ECC认证方式时，客户端需要执行'ecc local-key-pair create'命令来生成本地密钥对。", "reference_pages": ["41-50"], "difficulty": 2, "check": false}, {"id": 6, "question": "在ZTP配置中，DHCPv4 Server需要配置哪个Option来指定中间文件服务器的主机名？", "optionA": "Option 1", "optionB": "Option 3", "optionC": "Option 6", "optionD": "Option 66", "answer": "D", "reason": "根据手册中表1-43 Options字段说明，DHCPv4中Option 66用于指定中间文件服务器的主机名（TFTP/FTP/SFTP），而Option 1是子网掩码，Option 3是网关，Option 6是DNS服务器IP地址。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 7, "question": "在ZTP配置过程中，DHCPv6 Server需要配置哪个Option来指定中间文件路径？", "optionA": "Option 5", "optionB": "Option 23", "optionC": "Option 59", "optionD": "Option 67", "answer": "C", "reason": "根据手册中表1-44 Options字段说明，DHCPv6中Option 59用于指定中间文件路径，支持.ini、.py或.cfg扩展名，而Option 5是请求的IA地址、IPv6地址和生存期，Option 67是DHCPv4中用于指定文件服务器地址和中间文件名的选项。", "reference_pages": ["71-80", "81-85"], "difficulty": 2, "check": false}, {"id": 8, "question": "在ZTP配置中，关于DHCPv6地址租期的说法，以下哪项是正确的？", "optionA": "ZTP通过DHCPv6申请的IPv6地址租期至少为30分钟且支持续租", "optionB": "ZTP通过DHCPv6申请的IPv6地址租期至少为1小时且不支持续租", "optionC": "ZTP通过DHCPv6申请的IPv6地址租期至少为2小时且支持续租", "optionD": "ZTP通过DHCPv6申请的IPv6地址租期至少为24小时且不支持续租", "answer": "B", "reason": "根据手册中ZTP配置部分的说明，'ZTP通过DHCPv6申请的IPv6地址租期至少为1小时且不支持续租'。这是DHCPv6在ZTP环境中的特殊限制。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 9, "question": "在配置SCP客户端时，管理员需要设置SCP客户端的源地址，以下哪个命令是正确的？", "optionA": "scp source-ip *******", "optionB": "scp client-source -a *******", "optionC": "scp client-source -i LoopBack0", "optionD": "scp source-address *******", "answer": "B", "reason": "根据手册中SCP访问其他设备文件配置示例部分，设置SCP客户端源地址的正确命令是'scp client-source -a *******'，其中-a参数指定IP地址。", "reference_pages": ["61-70", "71-80"], "difficulty": 2, "check": false}, {"id": 10, "question": "在SSH服务器配置中，如果需要指定SSH服务器源接口，应使用以下哪个命令？", "optionA": "ssh server source GigabitEthernet0/0/0", "optionB": "ssh server-source -i GigabitEthernet0/0/0", "optionC": "ssh source-interface GigabitEthernet0/0/0", "optionD": "ssh source GigabitEthernet0/0/0", "answer": "B", "reason": "根据手册中SSH服务器配置示例，指定SSH服务器源接口的正确命令是'ssh server-source -i GigabitEthernet0/0/0'，其中-i参数指定接口名称。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 11, "question": "在配置SSH用户时，如果需要将用户client002分配RSA密钥rsakey001，应使用以下哪个命令？", "optionA": "ssh user client002 assign-key rsakey001", "optionB": "ssh user client002 bind rsa-key rsakey001", "optionC": "ssh user client002 assign rsa-key rsakey001", "optionD": "ssh user client002 key rsakey001", "answer": "C", "reason": "根据手册中SSH配置示例，将RSA密钥分配给SSH用户的正确命令是'ssh user client002 assign rsa-key rsakey001'。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 12, "question": "在ZTP配置中，关于文件服务器的安全建议，以下哪项是不正确的？", "optionA": "配置的文件服务器用户名应唯一", "optionB": "文件服务器用户权限应设置为只读", "optionC": "ZTP过程结束后应保持文件服务器功能开启以便后续维护", "optionD": "文件服务器与空配置设备的缺省网关之间必须路由可达", "answer": "C", "reason": "根据手册中ZTP配置部分的安全建议，'ZTP过程结束后，请关闭相应的文件服务器功能'，而不是保持开启。其他选项都是正确的安全建议。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 13, "question": "在SSH服务器配置中，如果需要设置SSH用户的服务类型为STelnet，应使用以下哪个命令？", "optionA": "ssh user client001 service-type telnet", "optionB": "ssh user client001 service-type ssh", "optionC": "ssh user client001 service-type stelnet", "optionD": "ssh user client001 service-type all", "answer": "C", "reason": "根据手册中SSH配置示例，设置SSH用户服务类型为STelnet的正确命令是'ssh user client001 service-type stelnet'。选项D虽然也包含STelnet服务，但不是专门为STelnet服务设置的。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 14, "question": "在配置SSH服务器支持其他端口号访问时，以下哪个命令可以设置SSH服务器监听端口号为2022？", "optionA": "ssh server port 2022", "optionB": "ssh server-port 2022", "optionC": "ssh port-server 2022", "optionD": "ssh listen-port 2022", "answer": "A", "reason": "根据手册中'配置SSH服务器支持其他端口号访问的示例'部分，设置SSH服务器监听端口号的正确命令是'ssh server port 2022'。", "reference_pages": ["51-60"], "difficulty": 2, "check": false}, {"id": 15, "question": "在ZTP配置中，如果需要配置DHCPv4 Server的Option 67来指定文件服务器地址和中间文件名，以下哪种格式是正确的？", "optionA": "sftp://user:password@***********/test.ini", "optionB": "sftp://***********/user/password/test.ini", "optionC": "sftp://***********:user:password/test.ini", "optionD": "sftp://***********/test.ini:user:password", "answer": "A", "reason": "根据手册中表1-45 DHCP服务器Option选项取值，Option 67指定文件服务器地址和中间文件名的正确格式是'sftp://user:password@***********/test.ini'，即用户名和密码在@符号前，IP地址在@符号后，文件路径在IP地址后。", "reference_pages": ["81-85"], "difficulty": 2, "check": false}, {"id": 16, "question": "在配置SSH客户端首次认证功能时，以下哪个命令是正确的？", "optionA": "ssh first-time enable", "optionB": "ssh client first-time enable", "optionC": "ssh first-authentication enable", "optionD": "ssh client first-authentication enable", "answer": "B", "reason": "根据手册中多个SSH客户端配置示例，启用SSH客户端首次认证功能的正确命令是'ssh client first-time enable'。", "reference_pages": ["1-10", "41-50"], "difficulty": 2, "check": false}, {"id": 17, "question": "在ZTP配置中，中间文件支持哪些扩展名？", "optionA": ".txt, .cfg, .ini", "optionB": ".ini, .py, .cfg", "optionC": ".py, .sh, .bat", "optionD": ".cfg, .xml, .json", "answer": "B", "reason": "根据手册中表1-44 Options字段说明，ZTP中间文件支持的扩展名是.ini、.py或.cfg。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 18, "question": "在SSH服务器配置中，如果需要设置SSH授权类型为default root，应使用以下哪个命令？", "optionA": "ssh authorization default root", "optionB": "ssh authorization-type default root", "optionC": "ssh auth-type default root", "optionD": "ssh auth default root", "answer": "B", "reason": "根据手册中SSH配置示例，设置SSH授权类型为default root的正确命令是'ssh authorization-type default root'。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 19, "question": "在配置SSH客户端公钥算法时，以下哪个命令可以同时启用RSA SHA-256和RSA SHA-512算法？", "optionA": "ssh client publickey rsa sha256 sha512", "optionB": "ssh client publickey rsa_sha256 rsa_sha512", "optionC": "ssh client publickey rsa_sha2_256 rsa_sha2_512", "optionD": "ssh client publickey rsa-sha2-256 rsa-sha2-512", "answer": "C", "reason": "根据手册中SSH配置示例，配置SSH客户端支持RSA SHA-256和RSA SHA-512公钥算法的正确命令是'ssh client publickey rsa_sha2_256 rsa_sha2_512'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 20, "question": "在ZTP配置中，关于ZTPv4的使用限制，以下哪项是正确的？", "optionA": "ZTPv4支持QinQ接口VLAN学习", "optionB": "ZTP运行过程中可以执行set save-configuration命令", "optionC": "ZTPv4不支持QinQ接口VLAN学习", "optionD": "ZTP只支持NE40E-X8产品系列", "answer": "C", "reason": "根据手册中表1-42 本特性的使用限制，ZTPv4不支持QinQ接口VLAN学习，且ZTP运行过程中禁止执行set save-configuration命令。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 21, "question": "在配置SCP客户端下载文件时，如果需要使用aes128加密算法，以下哪个命令格式是正确的？", "optionA": "scp -a ******* -encrypt aes128 user@ip:file localfile", "optionB": "scp -a ******* -cipher aes128 user@ip:file localfile", "optionC": "scp -a ******* -algorithm aes128 user@ip:file localfile", "optionD": "scp -a ******* -crypto aes128 user@ip:file localfile", "answer": "B", "reason": "根据手册中SCP访问其他设备文件配置示例，使用aes128加密算法下载文件的正确命令格式是'scp -a ******* -cipher aes128 user@ip:file localfile'，其中-cipher参数指定加密算法。", "reference_pages": ["61-70", "71-80"], "difficulty": 2, "check": false}, {"id": 22, "question": "在SSH服务器配置中，启用SCP服务器功能的命令是什么？", "optionA": "scp enable", "optionB": "scp server enable", "optionC": "ssh scp-server enable", "optionD": "ssh scp enable", "answer": "B", "reason": "根据手册中SCP访问其他设备文件配置示例，启用SCP服务器功能的正确命令是'scp server enable'。", "reference_pages": ["61-70", "71-80"], "difficulty": 2, "check": false}, {"id": 23, "question": "在配置SSH服务器支持的HMAC算法时，以下哪个命令可以同时启用SHA-512和SHA-256算法？", "optionA": "ssh server hmac sha512 sha256", "optionB": "ssh server hmac sha2-512 sha2-256", "optionC": "ssh server hmac sha2_512 sha2_256", "optionD": "ssh server hmac sha-512 sha-256", "answer": "C", "reason": "根据手册中SSH配置示例，配置SSH服务器支持SHA-512和SHA-256 HMAC算法的正确命令是'ssh server hmac sha2_512 sha2_256'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 24, "question": "在配置SSH服务器支持的加密算法时，以下哪个命令可以同时启用AES-256-GCM和AES-128-GCM算法？", "optionA": "ssh server cipher aes256-gcm aes128-gcm", "optionB": "ssh server cipher aes256_gcm aes128_gcm", "optionC": "ssh server cipher aes-256-gcm aes-128-gcm", "optionD": "ssh server cipher aes_256_gcm aes_128_gcm", "answer": "B", "reason": "根据手册中SSH配置示例，配置SSH服务器支持AES-256-GCM和AES-128-GCM加密算法的正确命令是'ssh server cipher aes256_gcm aes128_gcm'。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 25, "question": "在配置SSH服务器支持的密钥交换算法时，以下哪个命令可以启用DH Group Exchange SHA-256算法？", "optionA": "ssh server key-exchange dh-group-exchange-sha256", "optionB": "ssh server key-exchange dh_group_exchange_sha256", "optionC": "ssh server key-exchange diffie-hellman-group-exchange-sha256", "optionD": "ssh server key-exchange diffie_hellman_group_exchange_sha256", "answer": "B", "reason": "根据手册中SSH配置示例，配置SSH服务器支持DH Group Exchange SHA-256密钥交换算法的正确命令是'ssh server key-exchange dh_group_exchange_sha256'。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 26, "question": "在ZTP配置中，如果待配置设备与DHCPv6服务器不在同一网段，需要采取什么措施？", "optionA": "配置静态路由", "optionB": "配置DHCPv6中继", "optionC": "使用DHCPv4代替DHCPv6", "optionD": "配置IPv6隧道", "answer": "B", "reason": "根据手册中ZTP配置部分的说明，'当待配置设备与DHCPv6服务器不在同一网段时，需要配置DHCPv6中继以转发DHCPv6的交互报文'。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 27, "question": "在配置SSH用户认证方式时，以下哪个命令可以设置用户client001的认证方式为密码认证？", "optionA": "ssh user client001 authentication password", "optionB": "ssh user client001 auth-type password", "optionC": "ssh user client001 authentication-type password", "optionD": "ssh user client001 auth password", "answer": "C", "reason": "根据手册中SSH配置示例，设置SSH用户认证方式为密码认证的正确命令是'ssh user client001 authentication-type password'。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 28, "question": "在配置SSH服务器支持的公钥算法时，以下哪个命令可以同时启用RSA SHA-256和RSA SHA-512算法？", "optionA": "ssh server publickey rsa sha256 sha512", "optionB": "ssh server publickey rsa_sha256 rsa_sha512", "optionC": "ssh server publickey rsa_sha2_256 rsa_sha2_512", "optionD": "ssh server publickey rsa-sha2-256 rsa-sha2-512", "answer": "C", "reason": "根据手册中SSH配置示例，配置SSH服务器支持RSA SHA-256和RSA SHA-512公钥算法的正确命令是'ssh server publickey rsa_sha2_256 rsa_sha2_512'。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 29, "question": "在ZTP配置中，关于文件服务器的说法，以下哪项是正确的？", "optionA": "文件服务器只能是TFTP服务器", "optionB": "文件服务器只能是FTP服务器", "optionC": "文件服务器只能是SFTP服务器", "optionD": "文件服务器可以是TFTP/FTP/SFTP服务器", "answer": "D", "reason": "根据手册中ZTP配置部分的说明，'文件服务器可以是TFTP/FTP/SFTP服务器'，用户可以根据需要选择不同类型的文件服务器。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 30, "question": "在配置SSH服务器时，以下哪个命令可以启用STelnet服务器功能？", "optionA": "ssh server enable", "optionB": "stelnet server enable", "optionC": "telnet server enable", "optionD": "ssh stelnet-server enable", "answer": "B", "reason": "根据手册中SSH配置示例，启用STelnet服务器功能的正确命令是'stelnet server enable'。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 31, "question": "在配置VTY接口支持SSH协议时，以下哪个命令是正确的？", "optionA": "protocol inbound ssh", "optionB": "protocol ssh", "optionC": "inbound protocol ssh", "optionD": "ssh protocol enable", "answer": "A", "reason": "根据手册中SSH配置示例，在VTY接口下配置支持SSH协议的正确命令是'protocol inbound ssh'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 32, "question": "在ZTP配置中，DHCPv4 Server需要配置哪些必选的Options字段？", "optionA": "Option 1, Option 3", "optionB": "Option 3, Option 6", "optionC": "Option 1, Option 3, Option 6", "optionD": "Option 1, Option 3, Op<PERSON> 66", "answer": "A", "reason": "根据手册中表1-43 Options字段说明，DHCPv4中必选的Options字段是Option 1（子网掩码）和Option 3（网关），而Option 6（DNS服务器IP地址）和Option 66（中间文件服务器的主机名）是可选的。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 33, "question": "在配置SSH用户时，如果需要创建一个名为client002的SSH用户，并设置认证方式为ECC，以下哪个命令序列是正确的？", "optionA": "ssh user client002; ssh user client002 authentication-type ecc", "optionB": "ssh user client002 create; ssh user client002 auth-type ecc", "optionC": "ssh add-user client002; ssh user client002 authentication-type ecc", "optionD": "ssh create-user client002; ssh user client002 auth-type ecc", "answer": "A", "reason": "根据手册中SSH配置示例，创建SSH用户并设置认证方式的正确命令序列是'ssh user client002; ssh user client002 authentication-type ecc'。", "reference_pages": ["11-20"], "difficulty": 2, "check": false}, {"id": 34, "question": "在ZTP配置中，关于中间文件的说法，以下哪项是不正确的？", "optionA": "中间文件可以存放在文件服务器上", "optionB": "中间文件和版本文件可以部署在同一个文件服务器上", "optionC": "中间文件必须使用.ini扩展名", "optionD": "中间文件可以通过DHCPv4/DHCPv6服务器的Options字段指定", "answer": "C", "reason": "根据手册中ZTP配置部分和表1-44的说明，中间文件支持.ini、.py或.cfg扩展名，而不是必须使用.ini扩展名。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 35, "question": "在配置SSH服务器时，如果需要设置SSH用户的权限级别为3，应在哪个配置视图下执行命令？", "optionA": "系统视图", "optionB": "SSH视图", "optionC": "AAA视图", "optionD": "用户视图", "answer": "C", "reason": "根据手册中SSH配置示例，设置SSH用户权限级别的命令'local-user client001 level 3'是在AAA视图下执行的。", "reference_pages": ["61-70", "71-80"], "difficulty": 2, "check": false}, {"id": 36, "question": "在配置SSH服务器时，以下哪个命令可以设置VTY接口的认证方式为AAA认证？", "optionA": "authentication-mode aaa", "optionB": "authentication aaa", "optionC": "auth-mode aaa", "optionD": "aaa authentication", "answer": "A", "reason": "根据手册中SSH配置示例，在VTY接口下设置认证方式为AAA认证的正确命令是'authentication-mode aaa'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 37, "question": "在ZTP配置中，如果需要开启ZTP功能，以下哪个命令是正确的？", "optionA": "ztp enable", "optionB": "ztp start", "optionC": "ztp function enable", "optionD": "ztp service start", "answer": "A", "reason": "根据手册中ZTP配置部分的步骤说明，开启ZTP功能的正确命令应该是'ztp enable'。", "reference_pages": ["81-85"], "difficulty": 2, "check": false}, {"id": 38, "question": "在配置本地用户服务类型时，以下哪个命令可以设置用户client001的服务类型为SSH？", "optionA": "local-user client001 service ssh", "optionB": "local-user client001 service-type ssh", "optionC": "local-user client001 type ssh", "optionD": "local-user client001 ssh-service", "answer": "B", "reason": "根据手册中SSH配置示例，设置本地用户服务类型为SSH的正确命令是'local-user client001 service-type ssh'。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 39, "question": "在SFTP访问配置中，如果使用ECC认证方式，服务器端需要执行哪个命令来导入客户端的公钥？", "optionA": "ecc peer-public-key client002 import", "optionB": "peer-public-key ecc client002 import", "optionC": "public-key ecc import client002", "optionD": "import ecc public-key client002", "answer": "A", "reason": "根据手册中SFTP访问其他设备文件配置示例（ECC认证方式）部分，服务器端导入客户端ECC公钥的正确命令是'ecc peer-public-key client002 import'。", "reference_pages": ["41-50"], "difficulty": 2, "check": false}, {"id": 40, "question": "在ZTP配置中，关于文件服务器与空配置设备的连接要求，以下哪项是正确的？", "optionA": "文件服务器与空配置设备必须在同一网段", "optionB": "文件服务器与空配置设备的缺省网关之间必须路由可达", "optionC": "文件服务器必须与DHCP服务器部署在同一设备上", "optionD": "文件服务器必须支持HTTPS协议", "answer": "B", "reason": "根据手册中ZTP配置部分的说明，'文件服务器与空配置设备的缺省网关之间必须路由可达'，这是确保ZTP过程中设备能够正常下载文件的必要条件。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 41, "question": "在配置SSH服务器支持其他端口号访问时，默认的SSH服务器端口号是多少？", "optionA": "21", "optionB": "22", "optionC": "23", "optionD": "25", "answer": "B", "reason": "根据手册中'配置SSH服务器支持其他端口号访问的示例'部分，SSH服务器的默认端口号是22。", "reference_pages": ["51-60"], "difficulty": 2, "check": false}, {"id": 42, "question": "在ZTP配置中，如果需要配置自动补丁修复功能，这个步骤是必选的还是可选的？", "optionA": "必选步骤", "optionB": "可选步骤", "optionC": "仅在使用DHCPv6时必选", "optionD": "仅在使用DHCPv4时必选", "answer": "B", "reason": "根据手册中ZTP配置部分的步骤说明，'1.9.3.7（可选）配置自动补丁修复'是一个可选步骤，而不是必选步骤。", "reference_pages": ["81-85"], "difficulty": 2, "check": false}, {"id": 43, "question": "在配置SSH用户时，如果需要设置用户的服务类型为所有SSH服务，以下哪个命令是正确的？", "optionA": "ssh user client001 service-type ssh-all", "optionB": "ssh user client001 service-type all-ssh", "optionC": "ssh user client001 service-type all", "optionD": "ssh user client001 service-type any", "answer": "C", "reason": "根据手册中SSH配置示例，设置SSH用户服务类型为所有SSH服务的正确命令是'ssh user client001 service-type all'。", "reference_pages": ["61-70", "71-80"], "difficulty": 2, "check": false}, {"id": 44, "question": "在ZTP配置中，如果需要加载预配置脚本，这个步骤是必选的还是可选的？", "optionA": "必选步骤", "optionB": "可选步骤", "optionC": "仅在使用DHCPv6时必选", "optionD": "仅在使用DHCPv4时必选", "answer": "B", "reason": "根据手册中ZTP配置部分的步骤说明，'1.9.3.6（可选）加载预配置脚本'是一个可选步骤，而不是必选步骤。", "reference_pages": ["81-85"], "difficulty": 2, "check": false}, {"id": 45, "question": "在配置SSH服务器时，以下哪个命令可以设置SSH用户client001的认证方式为RSA？", "optionA": "ssh user client001 authentication rsa", "optionB": "ssh user client001 auth-type rsa", "optionC": "ssh user client001 authentication-type rsa", "optionD": "ssh user client001 auth rsa", "answer": "C", "reason": "根据手册中SSH配置示例，设置SSH用户认证方式为RSA的正确命令是'ssh user client001 authentication-type rsa'。", "reference_pages": ["1-10"], "difficulty": 2, "check": false}, {"id": 46, "question": "在ZTP配置中，DHCPv6 Server需要配置哪些必选的Options字段？", "optionA": "Option 5", "optionB": "Option 59", "optionC": "Option 5, Option 59", "optionD": "Option 5, Option 23", "answer": "A", "reason": "根据手册中表1-44 Options字段说明，DHCPv6中必选的Options字段是Option 5（请求的IA地址、IPv6地址和生存期），而Option 59（中间文件路径）是可选的。", "reference_pages": ["71-80"], "difficulty": 2, "check": false}, {"id": 47, "question": "在配置SSH客户端支持的加密算法时，以下哪个命令可以同时启用AES-256-CTR和AES-192-CTR算法？", "optionA": "ssh client cipher aes256-ctr aes192-ctr", "optionB": "ssh client cipher aes256_ctr aes192_ctr", "optionC": "ssh client cipher aes-256-ctr aes-192-ctr", "optionD": "ssh client cipher aes_256_ctr aes_192_ctr", "answer": "B", "reason": "根据手册中SSH配置示例，配置SSH客户端支持AES-256-CTR和AES-192-CTR加密算法的正确命令是'ssh client cipher aes256_ctr aes192_ctr'。", "reference_pages": ["1-10", "11-20"], "difficulty": 2, "check": false}, {"id": 48, "question": "在配置SSH客户端支持的HMAC算法时，以下哪个命令可以同时启用SHA-512和SHA-256算法？", "optionA": "ssh client hmac sha512 sha256", "optionB": "ssh client hmac sha2-512 sha2-256", "optionC": "ssh client hmac sha2_512 sha2_256", "optionD": "ssh client hmac sha-512 sha-256", "answer": "C", "reason": "根据手册中SSH配置示例，配置SSH客户端支持SHA-512和SHA-256 HMAC算法的正确命令是'ssh client hmac sha2_512 sha2_256'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 49, "question": "在配置SSH客户端支持的密钥交换算法时，以下哪个命令可以启用DH Group Exchange SHA-256算法？", "optionA": "ssh client key-exchange dh-group-exchange-sha256", "optionB": "ssh client key-exchange dh_group_exchange_sha256", "optionC": "ssh client key-exchange diffie-hellman-group-exchange-sha256", "optionD": "ssh client key-exchange diffie_hellman_group_exchange_sha256", "answer": "B", "reason": "根据手册中SSH配置示例，配置SSH客户端支持DH Group Exchange SHA-256密钥交换算法的正确命令是'ssh client key-exchange dh_group_exchange_sha256'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}, {"id": 50, "question": "在配置SSH客户端支持的公钥算法时，以下哪个命令可以同时启用DSA、ECC和RSA算法？", "optionA": "ssh client publickey dsa ecc rsa", "optionB": "ssh client publickey dsa,ecc,rsa", "optionC": "ssh client publickey-algorithm dsa ecc rsa", "optionD": "ssh client public-key dsa ecc rsa", "answer": "A", "reason": "根据手册中SSH配置示例，配置SSH客户端支持DSA、ECC和RSA公钥算法的正确命令是'ssh client publickey dsa ecc rsa'。", "reference_pages": ["1-10", "71-80"], "difficulty": 2, "check": false}]