页码范围: 21 - 30


--- 第 21 页 ---

----------------------------------------------------
Username                : client001
Authentication-type     : password
User-public-key-name    : 
User-public-key-type    : -
Sftp-directory          : -
Service-type            : stelnet
Username                : client002
Authentication-type     : sm2
User-public-key-name    : sm2key001
User-public-key-type    : SM2
Sftp-directory          : -
Service-type            : stelnet
----------------------------------------------------
Total 2, 2 printed
----结束
配置文件
● SSH 服务器的配置文件
#
sysname SSH Server
#  
interface GigabitEthernet0/0/0   
 undo shutdown 
 ip address ******** *********** 
#
sm2 peer-public-key sm2key001
 public-key-code begin
    0474F110 F90F131B B6F6D929 9A23A41E F1AB1666
    AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F
    DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC
    BC28A576 7E
 public-key-code end
 peer-public-key end
#
stelnet server enable
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 authentication-type sm2
ssh user client002 assign sm2-key sm2key001
ssh user client002 service-type stelnet
ssh server-source -i GigabitEthernet0/0/0
ssh server assign sm2-host-key sm2key001
ssh authorization-type default root
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
ssh server hmac sha2_512 sha2_256 
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
aaa
 local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
 local-user client001 service-type ssh
 #HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 297
--------------------------------------------------

--- 第 22 页 ---

user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
 user privilege level 3
#
return
● SSH 客户端 Client001 的配置文件
#
 sysname client001
#
interface GigabitEthernet0/0/0
 ip address ******** ***********
#
ssh client first-time  enable
#
return
● SSH 客户端 Client002 的配置文件
#
 sysname client002
#
interface GigabitEthernet0/0/0
 ip address ******** ***********
#
ssh client first-time  enable
#
ssh client assign sm2-host-key sm2key001
#
ssh client publickey sm2
#
return
******** 通过TFTP访问其他设备文件配置示例
在本示例中，通过在 TFTP服务器端运行 TFTP软件，并设置源文件在服务器中的位置，
实现文件的上传和下载。
组网需求
FTP是TCP/IP协议族中最常用的文件传送协议，但是终端和服务器之间交互复杂，对于
没有先进操作系统的终端而言实现文件传输相当困难。因此， TFTP应运而生，它提供
不复杂、开销不大的服务，是专为终端和服务器间不需要复杂交互而设计。但是 TFTP
只限于简单文件传送操作，不提供存取授权。
如图1-69所示，从终端登录到 TFTP客户端，再从 TFTP服务器上传、下载文件。
图1-69 配置通过 TFTP访问其他设备文件组网图
 HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 298
--------------------------------------------------

--- 第 23 页 ---

注意事项
当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
********* 通过SFTP访问其他设备文件配置示例（ ECC认证方式） 。
配置思路
采用如下的思路配置通过 TFTP访问其他设备文件：
1.在TFTP服务器端运行 TFTP软件，并设置源文件在服务器中的位置。
2.在TFTP客户端上使用 TFTP命令实现下载文件。
3.在TFTP客户端上使用 TFTP命令实现上传文件。
数据准备
为完成此配置例，需准备如下的数据：
● TFTP 服务器端安装 TFTP软件。
●源文件在 TFTP服务器中的路径。
●目标文件名及在 TFTP客户端存放的路径。
操作步骤
步骤1启动TFTP服务器
设置TFTP服务器的 Current Directory 目录为下载文件所在的目录，界面如 图1-70所
示。
图1-70 设置TFTP服务器基础目录
 HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 299
--------------------------------------------------

--- 第 24 页 ---

说明
由于计算机使用的 TFTP服务器软件不同，屏幕显示可能不同。
在远端系统中启动 tftpservermt 登录该系统，进入 TFTP服务器路径，并执行如下命
令。
/home/<USER>/tftpserver -v -i tftpserver.ini
TFTP Server MultiThreaded Version 1.61 Unix Built 1611
starting TFTP...
username: root
alias / is mapped to /home/
permitted clients: all
server port range: all
max blksize: 65464
default blksize: 512
default timeout: 3
file read allowed: Yes
file create allowed: Yes
file overwrite allowed: Yes
thread pool size: 1
listening on: 0.0.0.0:69
Accepting requests..
步骤2通过计算机终端仿真程序登录到 TFTP客户端下载文件。
<HUAWEI> tftp 10.18.26.141 get a.txt cfcard:/b.txt
Warning: cfcard:/b.txt  exists, overwrite? Please select
[Y/N]:y
Transfer file in binary mode.
Please wait for a while...
/
3338 bytes transferred
File transfer completed
步骤3验证配置结果
在TFTP客户端上执行命令 dir，可以看到下载的目标文件存在于指定的目录。
<HUAWEI> dir
Directory of 0/17#cfcard:/
  Idx  Attr     Size(Byte)  Date        Time(LMT)   FileName
    0  -rw-     3,338       Jan 25 2011  09:27:41   b.txt
    1  -rw-     103,265,123 Jan 25 2011  06:49:07   V800R023C10SPC500B020D0123.cc
    2  -rw-     92,766,274  Jan 25 2011  06:49:10   V800R023C10SPC500SPC007B008D1012.cc
109,867,396 KB total (102,926,652 KB free)
步骤4通过计算机终端仿真程序登录到 TFTP客户端上传文件。
<HUAWEI> tftp 10.111.16.160 put sample.txt
  Info: Transfer file in binary mode.
Please wait for a while...
\     100% [***********]
File transfer completed
----结束
配置文件
无
1.8.14.7 通过FTP访问其他设备文件配置示例
在本示例中，通过从 FTP客户端登录 FTP服务器，实现从 FTP服务器中下载系统软件和
配置文件到客户端。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 300
--------------------------------------------------

--- 第 25 页 ---

背景信息
说明
使用FTP协议存在安全风险，建议使用 SFTP方式访问其他设备文件。
组网需求
当用户需要与远程 FTP服务器进行文件传输、目录管理等操作时，可以配置当前设备为
FTP客户端，通过 FTP方式访问远程 FTP服务器，以实现远程管理和维护。
如图1-71所示，作为 FTP客户端的设备和 FTP服务器之间路由可达，用户可通过从 FTP
客户端登录 FTP服务器，实现从 FTP服务器中下载系统软件和配置文件到客户端。
图1-71 配置通过 FTP访问其他设备文件组网图
说明
本例中的 Interface1 代表接口 1/0/1。
配置思路
采用如下的思路配置通过 FTP访问其他设备文件：
1.配置用户登录 FTP服务器的用户名和密码及访问目录。
2.使能FTP服务器的 FTP功能。
3.使用FTP客户端登录命令实现登录 FTP服务器。
4.配置客户端文件传输方式和工作目录，实现从服务器下载用户所需文件。
数据准备
为完成此配置示例，需准备如下的数据：
● FTP客户端用于登录的用户名和密码。
● FTP服务器的 IP地址为 *******。
●目标文件及在 FTP客户端中的位置。
注意事项
当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
********* 通过SFTP访问其他设备文件配置示例（ ECC认证方式） 。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 301
--------------------------------------------------

--- 第 26 页 ---

操作步骤
步骤1在FTP服务器上配置 FTP用户。
<HUAWEI> system-view
[~HUAWEI] aaa
[*HUAWEI-aaa] local-user huawei password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
说明
设置的密码必须满足以下要求：
●密码采取交互式输入，系统不回显输入的密码。
●输入的密码为字符串形式，区分大小写，长度范围是 8～16。输入的密码至少包含两种类型
字符，包括大写字母、小写字母、数字及特殊字符。
●特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。
–如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
–如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。
配置文件中将以密文形式体现设置的密码。
[*HUAWEI-aaa] local-user huawei service-type ftp
[*HUAWEI-aaa] local-user huawei ftp-directory cfcard:/
[*HUAWEI-aaa] local-user huawei level 3
[*HUAWEI-aaa] commit
[~HUAWEI-aaa] quit
步骤2使能FTP服务功能
[~HUAWEI] interface LoopBack 0
[~HUAWEI-LoopBack0] ip address ******** ***************
[*HUAWEI-LoopBack0] quit
[*HUAWEI] ftp server enable
[*HUAWEI] ftp server-source  -i loopback 0
[*HUAWEI] commit
[~HUAWEI] quit
步骤3从FTP客户端登录到 FTP服务器。
<HUAWEI> ftp ********
Trying ******** ...
Press CTRL+K to abort
Connected to *******.
220 FTP service ready.
User(********:(none)):huawei
331 Password required for huawei.
Enter password:
230 User logged in.  
[ftp]
步骤4在FTP客户端上，配置二进制传输格式和 Flash工作目录。
[ ftp]  binary
200 Type set to I.
[ftp] lcd new_dir:/
The current local directory is new_dir:.
[ftp] commit
步骤5在FTP客户端上，从远端 FTP服务器下载最新系统软件。
[ftp] get V800R023C10SPC500B020D0123.cc
200 Port command okay.
150 Opening BINARY mode data connection for V800R023C10SPC500B020D0123.cc.
226 Transfer complete.
FTP: 1127 byte(s) received in 0.156 second(s) 7.22Kbyte(s)/sec.HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 302
--------------------------------------------------

--- 第 27 页 ---

[ftp] quit
可以使用 dir命令查看是否将所需的文件下载到客户端。
----结束
配置文件
● FTP服务器上的配置文件
#
aaa
 local-user huawei password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
 local-user huawei ftp-directory cfcard:/
 local-user huawei level 3
 local-user huawei service-type ftp
 #
interface GigabitEthernet1/0/1
 undo shutdown
 ip address ******* *************
#
interface loopback 0
 ip address ******** ***************
ftp server enable
ftp server-source -i loopback 0
#
return
● FTP客户端上的配置文件
#
interface GigabitEthernet1/0/1
 undo shutdown
 ip address ******* *************
#
return
******** 通过SFTP访问其他设备文件配置示例（ RSA认证方式）
在本示例中，通过在 SFTP客户端和 SSH服务器端生成本地密钥对，在 SSH服务器端生
成RSA公钥、并为用户绑定该 RSA公钥，实现 SFTP客户端连接 SSH服务器。
组网需求
SFTP建立在 SSH连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文
件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备提供了 SFTP客户
端功能，可以从本设备安全登录到远程 SSH服务器上，进行文件的安全传输。
如图1-72所示， SSH服务器端 SFTP服务使能后， SFTP客户端可以通过 RSA、DSA、
ECC、SM2、x509v3-ssh-rsa 、password 、password-rsa 、password-ecc 、password-
dsa、password-sm2 、password-x509v3-rsa 和all认证方式登录到 SSH服务器端进行文
件的访问。
图1-72 通过SFTP访问其他设备文件组网图
说明
本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 303
--------------------------------------------------

--- 第 28 页 ---

配置思路
采用如下思路配置 SFTP客户端连接 SSH服务器的示例：
1.在SSH服务器上配置用户 client001 和client002 ，分别使用不同的认证方式登录
SSH服务器。
2.分别在 SFTP客户端 Client002 和SSH服务器端生成本地密钥对，并为用户 client002
绑定SSH客户端的 RSA公钥，实现客户端登录服务器端时，对客户端进行验证。
3. SSH 服务器端 SFTP服务使能。
4.配置SSH用户的服务方式和授权目录。
5.用户client001 和client002 分别以 SFTP方式登录 SSH服务器，实现访问服务器上的
文件。
数据准备
为完成此配置举例，需准备如下的数据：
●用户Client001 ，登录验证方式为 password 。
●用户Client002 ，验证方式为 RSA，并为其分配公钥 rsakey001 。
● SSH 服务器的 IP地址为 ********
操作步骤
步骤1在服务器端生成本地密钥对
<HUAWEI>  system-view
[~HUAWEI]  sysname SSH Server
[*HUAWEI]  commit
[~SSH Server]  rsa local-key-pair create
The key name will be: SSH Server_Host  
The range of public key size is (2048, 3072).  
NOTE: Key pair generation will take a short while.  
Please input the modulus [default = 3072]:3072
步骤2在服务器端创建 SSH用户HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 304
--------------------------------------------------

--- 第 29 页 ---

说明
SSH用户主要有 Password 、RSA、password-rsa 、ECC、password-ecc 、DSA、password-dsa 、
SM2、password-sm2 或all这几种认证方式：
●如果SSH用户的认证方式为 password 、password-rsa 、password-dsa 、password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。
●如果SSH用户的认证方式为 RSA、password-rsa 、DSA、password-dsa 、SM2、password-
sm2、ECC、password-ecc 和all，服务器端应保存 SSH客户端的 RSA、DSA、SM2或ECC公
钥。
# 配置VTY用户界面。
[~SSH Server]  user-interface vty 0 4
[~SSH Server-ui-vty0-4]  authentication-mode aaa
[*SSH Server-ui-vty0-4]  protocol inbound ssh
[*SSH Server-ui-vty0-4]  commit
[~SSH Server-ui-vty0-4]  quit
●创建SSH用户Client001 。
# 新建用户名为 Client001 的SSH用户，且认证方式为 password 。
[~SSH Server]  ssh user client001
[*SSH Server]  ssh user client001 authentication-type password
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server]  commit
# 为SSH用户Client001 配置密码。
[~SSH Server]  aaa
[*SSH Server-aaa]  local-user client001 password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
说明
设置的密码必须满足以下要求：
–密码采取交互式输入，系统不回显输入的密码。
–输入的密码为字符串形式，区分大小写，长度范围是 8～16。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。
–特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。
▪如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
▪如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。
配置文件中将以密文形式体现设置的密码。
[*SSH Server-aaa]  local-user client001 service-type ssh
[*SSH Server-aaa]  local-user client001 level 3
[*SSH Server-aaa]  commit
[~SSH Server-aaa]  quit
●创建SSH用户Client002 。
# 新建用户名为 Client002 的SSH用户，且认证方式为 RSA。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 305
--------------------------------------------------

--- 第 30 页 ---

[~SSH Server]  ssh user client002
[*SSH Server]  ssh user client002 authentication-type rsa
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server] ssh authorization-type default root
[*SSH Server]  commit
步骤3配置服务器端 RSA公钥
# 客户端生成客户端的本地密钥对
<HUAWEI> system-view
[~HUAWEI] sysname client002
[*HUAWEI] commit
[~client002] rsa local-key-pair create
The key name will be: client002_Host  
The range of public key size is (2048, 3072).  
NOTE: Key pair generation will take a short while.  
Please input the modulus [default = 3072]:3072
[*client002]  commit
# 查看客户端上生成 RSA公钥。
[~client002]  display rsa local-key-pair public
======================Host Key==========================
Time of Key pair created : 13:22:1 2010/10/25
Key Name : client002_Host
Key Type : RSA Encryption Key
========================================================
Key Code: 
308188
  028180
    B21315DD 859AD7E4 A6D0D9B8 121F23F0 006BB1BB
    A443130F 7CDB95D8 4A4AE2F3 D94A73D7 36FDFD5F
    411B8B73 3CDD494A 236F35AB 9BBFE19A 7336150B
    40A35DE6 2C6A82D7 5C5F2C36 67FBC275 2DF7E4C5
    1987178B 8C364D57 DD0AA24A A0C2F87F 474C7931
    A9F7E8FE E0D5A1B5 092F7112 660BD153 7FB7D5B2
    171896FB 1FFC38CD 
  0203
    010001
Host Public Key for PEM format Code: 
---- BEGIN SSH2 PUBLIC KEY ----
AAAAB3NzaC1yc2EAAAADAQABAAAAgQCyExXdhZrX5KbQ2bgSHyPwAGuxu6RDEw98
25XYSkri89lKc9c2/f1fQRuLczzdSUojbzWrm7/hmnM2FQtAo13mLGqC11xfLDZn
+8J1LffkxRmHF4uMNk1X3QqiSqDC+H9HTHkxqffo/uDVobUJL3ESZgvRU3+31bIX
GJb7H/w4zQ==
---- END SSH2 PUBLIC KEY ----
Public key code for pasting into OpenSSH authorized_keys file: 
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAgQCyExXdhZrX5KbQ2bgSHyPwAGuxu6RDEw9825XYSkri
89lKc9c2/f1fQRuLczzdSUojbzWrm7/hmnM2FQtAo13mLGqC11xfLDZn+8J1LffkxRmHF4uMNk1X3Qqi
SqDC+H9HTHkxqffo/uDVobUJL3ESZgvRU3+31bIXGJb7H/w4zQ==  rsa-key
Host Public key for SSH1 format code: 
1024 65537 125048203250833642388841080101906750228075076456213955541037945628567
57310398880086451511608221218821171562865637463140847157102422109476944363593619
24637760514734544191988044752471924402237145321162849626052751701862381759745461
33321165741031171160914926309797395278974490949461701171569544048167828558985421HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 306
--------------------------------------------------
