页码范围: 11 - 20


--- 第 11 页 ---

3. SSH 服务器端 STelnet服务使能。
4.配置SSH用户Client001 和Client002 的服务方式为 STelnet。
5.使能SSH客户端首次认证功能。
6.用户Client001 和Client002 分别以 STelnet方式实现登录 SSH服务器。
数据准备
为完成此配置举例，需准备如下的数据：
●用户Client001 ，登录验证方式为 password 。
●用户Client002 ，验证方式为 ECC，并为其分配公钥 Ecckey001 。
● SSH 服务器的 IP地址为 ********。
操作步骤
步骤1在服务器端生成本地密钥对
<HUAWEI>  system-view
[~HUAWEI]  sysname SSH Server
[*HUAWEI]  commit
[~SSH Server]  ecc local-key-pair create
Info: The key name will be: SSH Server_Host_ECC
Info: The key modulus can be any one of the following: 256, 384, 521.
Info: Key pair generation will take a short while.
Please input the modulus [default=521]:521
Info: Generating keys...
Info: Succeeded in creating the ECC host keys.
步骤2在服务器端创建 SSH用户
说明
SSH用户主要有 Password 、RSA、password-rsa 、ECC、password-ecc 、DSA、password-dsa 、
SM2、password-sm2 或all这几种认证方式：
●如果SSH用户的认证方式为 password 、password-rsa 、password-dsa 、password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。
●如果SSH用户的认证方式为 RSA、password-rsa 、DSA、password-dsa 、SM2、password-
sm2、ECC、password-ecc 和all，服务器端应保存 SSH客户端的 RSA、DSA、SM2或ECC公
钥。
# 配置VTY用户界面。
[*SSH Server] user-interface vty 0 4
[*SSH Server-ui-vty0-4] authentication-mode aaa
[*SSH Server-ui-vty0-4] protocol inbound ssh
[*SSH Server-ui-vty0-4] user privilege level 3
[*SSH Server-ui-vty0-4] commit
[~SSH Server-ui-vty0-4] quit
●创建SSH用户Client001 。
# 新建用户名为 Client001 的SSH用户，且认证方式为 password 。
[~SSH Server] ssh user client001
[*SSH Server] ssh user client001 authentication-type password
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 287
--------------------------------------------------

--- 第 12 页 ---

[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server] commit
# 为SSH用户Client001 配置密码。
[~SSH Server] aaa
[*SSH Server-aaa] local-user client001 password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
说明
设置的密码必须满足以下要求：
–密码采取交互式输入，系统不回显输入的密码。
–输入的密码为字符串形式，区分大小写，长度范围是 8～16。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。
–特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。
▪如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
▪如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。
配置文件中将以密文形式体现设置的密码。
[*SSH Server-aaa] local-user client001 service-type ssh
[*SSH Server-aaa] commit
[~SSH Server-aaa] quit
●创建SSH用户Client002 。
# 新建用户名为 Client002 的SSH用户，且认证方式为 ECC。
[~SSH Server] ssh user client002
[*SSH Server] ssh user client002 authentication-type ecc
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server] ssh authorization-type default root
[*SSH Server] commit
步骤3配置服务器端 ECC公钥
# 客户端 Client002 生成客户端的本地密钥对
<HUAWEI> system-view
[~HUAWEI] sysname client002
[*HUAWEI] commit
[~client002] ecc local-key-pair create
Info: The key name will be: client002_Host_ECC
Info: The key modulus can be any one of the following: 256, 384, 521.
Info: Key pair generation will take a short while.
Please input the modulus [default=521]:521
Info: Generating keys...
Info: Succeeded in creating the ECC host keys.
[*client002]  commit
# 查看客户端上生成 ECC公钥。
[~client002] display ecc local-key-pair public
======================Host Key==========================
Time of Key pair created : 2013-01-22 10:33:06HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 288
--------------------------------------------------

--- 第 13 页 ---

Key Name : client002_Host_ECC
Key Type : ECC Encryption Key
========================================================
Key Code:                                                                                                                           
  04D7635B C047B02E 20C1E6CB E04B5E5C 
7DCADD88                                                                                      
  F676AB0E C91ACB3C B0394B18 FA29E5C2 
0426F924                                                                                      
  DAD9AA02 C531E5ED C6783FFA 41235A16 
8D7723E0                                                                                      
  7E63D68D E7  
Host Public Key for PEM format Code:
---- BEGIN SSH2 PUBLIC KEY ----
AAAAE2VjZHNhLXNoYTItbmlzdHAxOTIAAABBBL+PCqbAEJKKKUpCYdSfyiyY5Iq3
DM9ZB3mjx62wShmmNMiZJAV+02aMJ6CsHBuWCbVLO/Zg8Ng3kGXC4ltmLXM=
---- END SSH2 PUBLIC KEY ----
# 将客户端上产生的 ECC公钥传送到服务器端。
[~SSH Server] ecc peer-public-key Ecckey001
Enter "ECC public key" view, return system view with "peer-public-key end".
[*SSH Server-ecc-public-key] public-key-code begin
Enter "ECC key code" view, return last view with "public-key-code end".
[*SSH Server-ecc-public-key-ecc-key-code] 04BF8F0A A6C01092 8A294A42 61D49FCA 2C98E48A
[*SSH Server-ecc-public-key-ecc-key-code] B70CCF59 0779A3C7 ADB04A19 A634C899 24057ED3
[*SSH Server-ecc-public-key-ecc-key-code] 668C27A0 AC1C1B96 09B54B3B F660F0D8 379065C2
[*SSH Server-ecc-public-key-ecc-key-code] E25B662D 73
[*SSH Server-ecc-public-key-ecc-key-code] public-key-code end
[*SSH Server-ecc-public-key] peer-public-key end
[*SSH Server] commit
步骤4为SSH用户Client002 绑定SSH客户端的 ECC公钥。
[~SSH Server] ssh user client002 assign ecc-key ecckey001
[*SSH Server] commit
步骤5SSH服务器端 STelnet服务使能
# 使能STelnet服务功能。
[~SSH Server] stelnet server enable
[*SSH Server] ssh server-source  -i GigabitEthernet0/0/0
[*SSH Server] commit
步骤6配置SSH用户Client001 、Client002 的服务方式为 STelnet
[*SSH Server] ssh user client001 service-type stelnet
[*SSH Server] ssh user client002 service-type stelnet
[*SSH Server] commit
步骤7STelnet客户端连接 SSH服务器
# 第一次登录，需要使能 SSH客户端首次认证功能。
使能客户端 Client001 首次认证功能。
<HUAWEI> system-view
[~HUAWEI] sysname client001
[*HUAWEI] commit
[~client001] ssh client first-time  enable
[*client001] commit
使能客户端 Client002 首次认证功能
[~client002] ssh client first-time  enable
[*client002] commit
# STelnet 客户端 Client001 用password 认证方式连接 SSH服务器，输入配置的用户名和
密码。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 289
--------------------------------------------------

--- 第 14 页 ---

<~client001>  stelnet ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...
Please input the username:client001
Enter password:   
显示登录成功信息如下：
Info: The max number of VTY users is 20, and the number
      of current VTY users on line is 6.
      The current login time is 2011-01-06 11:42:42.
      First login successfully.
<SSH Server>
# STelnet 客户端 Client002 用ECC认证方式连接 SSH服务器。
<~client002>  stelnet ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...      
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait...    
Please input the username: client002
Info: The max number of VTY users is 20, and the number
      of current VTY users on line is 6.
      The current login time is 2011-01-06 11:42:42.
<SSH Server>
如果登录成功，用户将进入用户视图。如果登录失败，用户将收到 Session is
disconnected 的信息。
步骤8验证配置结果
# 查看SSH状态信息。
[~SSH Server] display ssh server status
SSH Version                                : 2.0
SSH authentication timeout (Seconds)       : 60
SSH authentication retries (Times)         : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility              : Enable
SSH server keepalive                       : Disable
SFTP IPv4 server                           : Enable
SFTP IPv6 server                           : Enable
STELNET IPv4 server                        : Enable
STELNET IPv6 server                        : Enable
SNETCONF IPv4 server                       : Enable
SNETCONF IPv6 server                       : Enable
SNETCONF IPv4 server port(830)             : Disable
SNETCONF IPv6 server port(830)             : Disable
SCP IPv4 server                            : Enable
SCP IPv6 server                            : Enable
SSH port forwarding                        : Disable
SSH IPv4 server port                       : 22
SSH IPv6 server port                       : 22
ACL name                                   :
ACL number                                 :
ACL6 name                                  : 
ACL6 number                                :
SSH server ip-block                        : Enable
# 查看SSH用户信息。
[~SSH Server] display ssh user-information
----------------------------------------------------
Username                : client001
Authentication-type     : passwordHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 290
--------------------------------------------------

--- 第 15 页 ---

User-public-key-name    : 
User-public-key-type    : -
Sftp-directory          : -
Service-type            : stelnet
Username                : client002
Authentication-type     : ecc
User-public-key-name    : ecckey001
User-public-key-type    : ECC
Sftp-directory          : -
Service-type            : stelnet
----------------------------------------------------
Total 2, 2 printed
----结束
配置文件
● SSH 服务器的配置文件
#
 sysname SSH Server
#
ecc peer-public-key ecckey001
public-key-code begin
    04BF8F0A A6C01092 8A294A42 61D49FCA 2C98E48A
    B70CCF59 0779A3C7 ADB04A19 A634C899 24057ED3
    668C27A0 AC1C1B96 09B54B3B F660F0D8 379065C2
    E25B662D 73
public-key-code end
peer-public-key end
#
stelnet server enable
ssh server-source -i GigabitEthernet0/0/0
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 assign ecc-key ecckey001
ssh user client002 authentication-type ecc
ssh authorization-type default root
ssh user client002 service-type stelnet
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
ssh server hmac sha2_512 sha2_256 
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
aaa
 local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
 local-user client001 service-type ssh
 #
user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
 user privilege level 3
#
return
● SSH 客户端 Client001 的配置文件HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 291
--------------------------------------------------

--- 第 16 页 ---

#
 sysname client001
#
interface GigabitEthernet0/0/0
 ip address ******** ***********
#
ssh client first-time  enable
#
return
● SSH 客户端 Client002 的配置文件
#
 sysname client002
#
interface GigabitEthernet0/0/0
 ip address ******** ***********
#
ssh client first-time  enable
#
return
******** 通过STelnet 登录其他设备配置示例（ SM2认证方式）
在本示例中，通过在 STelnet客户端和 SSH服务器端生成本地密钥对，在 SSH服务器端
生成SM2公钥、并为用户绑定该 SM2公钥，实现 Stelnet客户端连接 SSH服务器。
组网需求
网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是 Telnet缺少安全的认证方
式，而且传输过程采用 TCP进行简单方式传输，存在很大的安全隐患。
而STelnet是一种安全的 Telnet服务，建立在 SSH连接的基础之上。 SSH可以利用加密和
强大的认证功能提供安全保障，保护设备不受诸如 IP地址欺诈等攻击。 SSH服务器端
STelnet服务使能后， STelnet客户端可以通过 RSA、DSA、ECC、SM2、x509v3-ssh-
rsa、password 、password-rsa 、password-ecc 、password-dsa 、password-sm2 、
password-x509v3-rsa 和all认证方式登录到 SSH服务器端。如 图1-68所示，配置两个登
录用户为 client001 和client002 ，分别使用 password 方式和 SM2方式登录 SSH服务器。
图1-68 通过STelnet登录其他设备组网图
说明
本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。
HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 292
--------------------------------------------------

--- 第 17 页 ---

配置思路
采用如下的思路配置通过 STelnet登录其他设备：
1.在SSH服务器上配置用户 client001 和client002 ，分别使用不同的认证方式登录
SSH服务器。
2.分别在 STelnet客户端 Client002 和SSH服务器端生成本地密钥对，并为用户
client002 绑定SSH客户端的 SM2公钥，实现客户端登录服务器端时，对客户端进
行验证。
3. SSH 服务器端 STelnet服务使能。
4.配置SSH用户client001 和client002 的服务方式为 STelnet。
5.使能SSH客户端首次认证功能。
6.用户client001 和client002 分别以 STelnet方式实现登录 SSH服务器。
数据准备
为完成此配置举例，需准备如下的数据：
●用户client001 ，登录验证方式为 password 。
●用户client002 ，验证方式为 SM2，并为其分配公钥 sm2key001 。
● SSH 服务器的 IP地址为 ********。
操作步骤
步骤1在服务器端生成本地密钥对
<HUAWEI> system-view
[~HUAWEI] sysname SSH Server
[*HUAWEI] commit
[~SSH Server] ssh server publickey sm2
步骤2在服务器端创建 SSH用户
说明
SSH用户主要有 Password 、RSA、password-rsa 、ECC、password-ecc 、DSA、password-dsa 、
SM2、password-sm2 或all这几种认证方式：
●如果SSH用户的认证方式为 password 、password-rsa 、password-dsa 、password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。
●如果SSH用户的认证方式为 RSA、password-rsa 、DSA、password-dsa 、SM2、password-
sm2、ECC、password-ecc 和all，服务器端应保存 SSH客户端的 RSA、DSA、SM2或ECC公
钥。
# 配置VTY用户界面。
[*SSH Server] user-interface vty 0 4
[*SSH Server-ui-vty0-4] authentication-mode aaa
[*SSH Server-ui-vty0-4] protocol inbound ssh
[*SSH Server-ui-vty0-4] user privilege level 3
[*SSH Server-ui-vty0-4] commit
[~SSH Server-ui-vty0-4] quit
●创建SSH用户Client001 。
# 新建用户名为 Client001 的SSH用户，且认证方式为 password 。
[~SSH Server] ssh user client001
[*SSH Server] ssh user client001 authentication-type password
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 293
--------------------------------------------------

--- 第 18 页 ---

[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server] commit
# 为SSH用户Client001 配置密码。
[~SSH Server] aaa
[*SSH Server-aaa] local-user client001 password
Please configure  the password (8-128)
Enter Password:
Confirm  Password:
说明
设置的密码必须满足以下要求：
–密码采取交互式输入，系统不回显输入的密码。
–输入的密码为字符串形式，区分大小写，长度范围是 8～16。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。
–特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。
▪如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
▪如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。
配置文件中将以密文形式体现设置的密码。
[*SSH Server-aaa] local-user client001 service-type ssh
[*SSH Server-aaa] commit
[~SSH Server-aaa] quit
●创建SSH用户Client002 。
# 新建用户名为 Client002 的SSH用户，且认证方式为 SM2。
[~SSH Server] ssh user client002
[*SSH Server] ssh user client002 authentication-type sm2
[*SSH Server]  ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr  
[*SSH Server]  ssh server hmac sha2_512 sha2_256
[*SSH Server]  ssh server key-exchange dh_group_exchange_sha256  
[*SSH Server]  ssh server publickey rsa_sha2_256 rsa_sha2_512  
[*SSH Server]  ssh server dh-exchange min-len 3072
[*SSH Server]  ssh client publickey rsa_sha2_256 rsa_sha2_512
[*SSH Server]  ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr 
[*SSH Server]  ssh client hmac sha2_512 sha2_256 
[*SSH Server]  ssh client key-exchange dh_group_exchange_sha256  
[*SSH Server] ssh authorization-type default root
[*SSH Server] commit
步骤3为SSH服务器分配 SM2公钥
# 客户端 Client002 生成客户端的本地密钥对。
<HUAWEI> system-view
[~HUAWEI] sysname client002
[*HUAWEI] commit
[~client002] ssh client publickey sm2
[*client002] sm2 key-pair label sm2key001
Info: Key pair generation will take a short while. Please wait...
Info: Creating the key pair succeeded.
[*client002] ssh client assign sm2-host-key sm2key001
[*client002]  commit
# 查看客户端上生成 SM2公钥。HUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 294
--------------------------------------------------

--- 第 19 页 ---

[~client002] display sm2 key-pair
=====================================
 Label Name: sm2key001
 Modulus: 521
 Time of Key pair created: 2018-06-19 15:39:45
=====================================
Key : 
    0474F110 F90F131B B6F6D929 9A23A41E F1AB1666 AC4BE4EE EF2CD876
    2B633F80 DD5CF42F 147A722F DE527F39 247F3744 C23296BE FE3BE502
    EEF7D9EC BC28A576 7E
=====================================
# 将客户端上产生的 SM2公钥传送到服务器端。
[~SSH Server] sm2 peer-public-key sm2key001
Enter "SM2 public key" view, return system view with "peer-public-key end".
[*SSH Server-sm2-public-key] public-key-code begin
Enter "SM2 public key" view, return system view with "peer-public-key end".
[*SSH Server-sm2-public-key-sm2-key-code] 0474F110 F90F131B B6F6D929 9A23A41E F1AB1666
[*SSH Server-sm2-public-key-sm2-key-code] AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F
[*SSH Server-sm2-public-key-sm2-key-code] DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC
[*SSH Server-sm2-public-key-sm2-key-code] BC28A576 7E
[*SSH Server-sm2-public-key-sm2-key-code] public-key-code end
[*SSH Server-sm2-public-key] peer-public-key end
[*SSH Server] commit
步骤4为SSH用户Client002 绑定SSH客户端的 SM2公钥。
[~SSH Server] ssh user client002 assign sm2-key sm2key001
[*SSH Server] commit
步骤5分配SSH服务器端 SM2密钥对。
#SSH服务器端生成本地 SM2密钥对。
[~SSH Server] sm2 key-pair label sm2key001
#给SSH服务器分配 SM2密钥对。
[*SSH Server] ssh server assign sm2-host-key sm2key001
[*SSH Server] commit
步骤6SSH服务器端 STelnet服务使能
# 使能STelnet服务功能。
[~SSH Server] stelnet server enable
[*SSH Server] ssh server-source  -i GigabitEthernet0/0/0
[*SSH Server] commit
步骤7配置SSH用户Client001 、Client002 的服务方式为 STelnet
[*SSH Server] ssh user client001 service-type stelnet
[*SSH Server] ssh user client002 service-type stelnet
[*SSH Server] commit
步骤8STelnet客户端连接 SSH服务器
# 第一次登录，需要使能 SSH客户端首次认证功能。
使能客户端 Client001 首次认证功能。
<HUAWEI> system-view
[~HUAWEI] sysname client001
[*HUAWEI] commit
[~client001] ssh client first-time  enable
[*client001] commit
使能客户端 Client002 首次认证功能
[~client002] ssh client first-time  enableHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 295
--------------------------------------------------

--- 第 20 页 ---

[*client002] commit
# STelnet 客户端 Client001 用password 认证方式连接 SSH服务器，输入配置的用户名和
密码。
[~client001]  stelnet ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...
Please input the username:client001
Enter password:   
显示登录成功信息如下：
Info: The max number of VTY users is 20, and the number
      of current VTY users on line is 6.
      The current login time is 2018-07-16 11:42:42.
      First login successfully.
# STelnet 客户端 Client002 用SM2认证方式连接 SSH服务器。
<~client002>  stelnet ********
Trying ******** ...
Press CTRL+K to abort
Connected to ******** ...      
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait...    
Please input the username: client002
Info: The max number of VTY users is 20, and the number
      of current VTY users on line is 6.
      The current login time is 2018-07-16 11:42:42.
如果登录成功，用户将进入用户视图。如果登录失败，用户将收到 Session is
disconnected 的信息。
步骤9验证配置结果
# 查看SSH状态信息。
[~SSH Server] display ssh server status
SSH Version                                : 2.0
SSH authentication timeout (Seconds)       : 60
SSH authentication retries (Times)         : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility              : Enable
SSH server keepalive                       : Disable
SFTP IPv4 server                           : Enable
SFTP IPv6 server                           : Enable
STELNET IPv4 server                        : Enable
STELNET IPv6 server                        : Enable
SNETCONF IPv4 server                       : Enable
SNETCONF IPv6 server                       : Enable
SNETCONF IPv4 server port(830)             : Disable
SNETCONF IPv6 server port(830)             : Disable
SCP IPv4 server                            : Enable
SCP IPv6 server                            : Enable
SSH port forwarding                        : Disable
SSH IPv4 server port                       : 22
SSH IPv6 server port                       : 22
ACL name                                   :
ACL number                                 :
ACL6 name                                  : 
ACL6 number                                :
SSH server ip-block                        : Enable
# 查看SSH用户信息。
[~SSH Server] display ssh user-informationHUAWEI NetEngine40E
配置指南 1 基础配置
文档版本  01 (2024-03-31) 版权所有  © 华为技术有限公司 296
--------------------------------------------------
